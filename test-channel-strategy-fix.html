<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>渠道策略修复测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; color: #155724; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; color: #721c24; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; color: #0c5460; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
        button { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #0056b3; }
        .log { max-height: 300px; overflow-y: auto; background: #f8f9fa; padding: 10px; border: 1px solid #ddd; }
    </style>
</head>
<body>
    <h1>渠道策略修复测试</h1>
    
    <div class="test-section info">
        <h2>测试说明</h2>
        <p>这个测试将验证以下修复：</p>
        <ul>
            <li>Fliggy检测模式修复（支持冒号分隔）</li>
            <li>OTA集成中parseOrder方法的扩展</li>
            <li>字段提示词片段的正确注入</li>
            <li>端到端的渠道策略工作流程</li>
        </ul>
    </div>

    <div class="test-section">
        <h2>测试用例</h2>
        <h3>Fliggy订单数据</h3>
        <textarea id="testOrderData" rows="10" cols="80" readonly>订单编号：4673826324087252736买家：wujiexian124支付时间：2025-08-08 15:39:30
查看详情

经济7座

【送机】

新加坡-新加坡

[出发]新加坡庄家大酒店

[抵达]樟宜机场

约23.4公里

2025-08-10 04:00:00

吴洁娴

真实号：13771817391

---
3成人0儿童

司机姓名：---

司机电话：---

---

总价格：318元

用户实付：318.00元

商家实收：318元

待派单

14:21:51</textarea>
        <br><br>
        <button onclick="runChannelDetectionTest()">测试渠道检测</button>
        <button onclick="runStrategyTest()">测试策略获取</button>
        <button onclick="runFullIntegrationTest()">完整集成测试</button>
        <button onclick="clearResults()">清空结果</button>
    </div>

    <div class="test-section">
        <h2>测试结果</h2>
        <div id="testResults"></div>
    </div>

    <div class="test-section">
        <h2>日志输出</h2>
        <div id="logOutput" class="log"></div>
    </div>

    <script>
        const testOrderData = document.getElementById('testOrderData').value;
        const resultsDiv = document.getElementById('testResults');
        const logDiv = document.getElementById('logOutput');
        
        // 拦截console.log来显示日志
        const originalLog = console.log;
        console.log = function(...args) {
            originalLog.apply(console, args);
            const logEntry = document.createElement('div');
            logEntry.textContent = args.join(' ');
            logEntry.style.borderBottom = '1px solid #eee';
            logEntry.style.padding = '2px 0';
            logDiv.appendChild(logEntry);
            logDiv.scrollTop = logDiv.scrollHeight;
        };

        function clearResults() {
            resultsDiv.innerHTML = '';
            logDiv.innerHTML = '';
        }

        function addResult(title, content, type = 'info') {
            const div = document.createElement('div');
            div.className = `test-section ${type}`;
            div.innerHTML = `<h3>${title}</h3><pre>${content}</pre>`;
            resultsDiv.appendChild(div);
        }

        async function runChannelDetectionTest() {
            console.log('🔍 开始渠道检测测试...');
            
            try {
                // 检查依赖
                if (!window.OTA || !window.OTA.channelDetector) {
                    throw new Error('OTA渠道检测器未加载');
                }
                
                // 测试修复后的Fliggy检测
                const detectionResult = window.OTA.channelDetector.detectChannel(testOrderData);
                
                console.log('渠道检测结果:', detectionResult);
                
                const resultText = JSON.stringify(detectionResult, null, 2);
                
                if (detectionResult.detectedChannel === 'Fliggy') {
                    addResult('渠道检测测试', `✅ 成功检测到Fliggy渠道\n${resultText}`, 'success');
                } else {
                    addResult('渠道检测测试', `❌ 检测失败，未识别为Fliggy\n${resultText}`, 'error');
                }
                
            } catch (error) {
                console.error('渠道检测测试失败:', error);
                addResult('渠道检测测试', `❌ 测试失败: ${error.message}`, 'error');
            }
        }

        async function runStrategyTest() {
            console.log('🎯 开始策略测试...');
            
            try {
                // 检查FliggyOTAStrategy是否存在
                if (typeof FliggyOTAStrategy === 'undefined') {
                    throw new Error('FliggyOTAStrategy未加载');
                }
                
                // 获取字段提示词片段
                const fieldSnippets = FliggyOTAStrategy.getFieldPromptSnippets({ isRealtime: true });
                
                console.log('Fliggy策略字段片段:', fieldSnippets);
                
                const resultText = JSON.stringify(fieldSnippets, null, 2);
                
                // 检查关键字段
                const hasOta = fieldSnippets.ota && fieldSnippets.ota.includes('Fliggy');
                const hasOtaPrice = fieldSnippets.ota_price && fieldSnippets.ota_price.includes('价格识别');
                const hasCarTypeId = fieldSnippets.car_type_id && fieldSnippets.car_type_id.includes('车型ID映射');
                
                if (hasOta && hasOtaPrice && hasCarTypeId) {
                    addResult('策略测试', `✅ FliggyOTAStrategy字段片段正确\n${resultText}`, 'success');
                } else {
                    addResult('策略测试', `❌ 字段片段不完整\n${resultText}`, 'error');
                }
                
            } catch (error) {
                console.error('策略测试失败:', error);
                addResult('策略测试', `❌ 测试失败: ${error.message}`, 'error');
            }
        }

        async function runFullIntegrationTest() {
            console.log('🚀 开始完整集成测试...');
            
            try {
                // 检查所有依赖
                const dependencies = [
                    'window.OTA.channelDetector',
                    'window.OTA.otaGeminiIntegration',
                    'window.FliggyOTAStrategy'
                ];
                
                for (const dep of dependencies) {
                    const parts = dep.split('.');
                    let obj = window;
                    for (const part of parts.slice(1)) {
                        obj = obj[part];
                        if (!obj) {
                            throw new Error(`依赖缺失: ${dep}`);
                        }
                    }
                }
                
                console.log('✅ 所有依赖检查通过');
                
                // 测试渠道检测
                const detectionResult = window.OTA.channelDetector.detectChannel(testOrderData);
                console.log('渠道检测:', detectionResult);
                
                if (detectionResult.detectedChannel !== 'Fliggy') {
                    throw new Error('渠道检测失败，未识别为Fliggy');
                }
                
                // 测试字段片段获取
                const fieldSnippets = FliggyOTAStrategy.getFieldPromptSnippets({ isRealtime: true });
                console.log('字段片段:', fieldSnippets);
                
                // 模拟完整流程
                const testResult = {
                    步骤1_渠道检测: {
                        渠道: detectionResult.detectedChannel,
                        置信度: detectionResult.confidence,
                        检测方法: detectionResult.method
                    },
                    步骤2_策略选择: {
                        策略类: 'FliggyOTAStrategy',
                        字段片段数量: Object.keys(fieldSnippets).length,
                        包含字段: Object.keys(fieldSnippets)
                    },
                    步骤3_预期结果: {
                        ota: '应该返回 "Fliggy"',
                        ota_price: '应该应用马来西亚/新加坡价格规则',
                        car_type_id: '应该应用车型ID映射'
                    }
                };
                
                const resultText = JSON.stringify(testResult, null, 2);
                addResult('完整集成测试', `✅ 集成测试流程验证通过\n${resultText}`, 'success');
                
                console.log('🎉 完整集成测试完成');
                
            } catch (error) {
                console.error('完整集成测试失败:', error);
                addResult('完整集成测试', `❌ 测试失败: ${error.message}`, 'error');
            }
        }

        // 页面加载完成后自动运行基础检查
        window.addEventListener('load', function() {
            setTimeout(() => {
                console.log('🏁 页面加载完成，开始基础检查...');
                
                // 检查关键组件是否加载
                const components = [
                    { name: 'OTA命名空间', check: () => !!window.OTA },
                    { name: '渠道检测器', check: () => !!window.OTA?.channelDetector },
                    { name: 'Fliggy策略', check: () => !!window.FliggyOTAStrategy },
                    { name: 'OTA集成', check: () => !!window.OTA?.otaGeminiIntegration }
                ];
                
                components.forEach(component => {
                    if (component.check()) {
                        console.log(`✅ ${component.name} 已加载`);
                    } else {
                        console.log(`❌ ${component.name} 未加载`);
                    }
                });
            }, 1000);
        });
    </script>
</body>
</html>