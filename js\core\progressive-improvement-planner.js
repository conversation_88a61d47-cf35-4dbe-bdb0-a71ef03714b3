/**
 * 渐进式改进路径规划器
 * 
 * 设计目标：
 * - 规划最小化风险的改进路径
 * - 确定每个改进步骤的最小粒度
 * - 建立每步的验证标准
 * - 设计回滚策略
 * 
 * <AUTHOR>
 * @version 1.0.0
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};
window.OTA.core = window.OTA.core || {};

(function() {
    'use strict';

    /**
     * 渐进式改进路径规划器
     */
    class ProgressiveImprovementPlanner {
        constructor() {
            this.improvementPlan = this.createImprovementPlan();
            this.currentStep = 0;
            this.executionHistory = [];
            this.logger = this.getLogger();
            this.featureToggle = window.OTA.featureToggle;
            this.hotRollback = window.OTA.hotRollback;
            
            this.logger.log('渐进式改进路径规划器已初始化');
        }

        /**
         * 创建改进计划
         * @returns {Array} 改进步骤数组
         */
        createImprovementPlan() {
            return [
                // 阶段1：基础适配器验证
                {
                    id: 'step-001',
                    name: 'BaseManager适配器验证',
                    description: '验证BaseManager适配器功能正常',
                    type: 'validation',
                    riskLevel: 'low',
                    estimatedTime: 5, // 分钟
                    prerequisites: [],
                    actions: [
                        '验证BaseManager适配器加载',
                        '测试日志功能',
                        '测试事件功能',
                        '验证错误处理'
                    ],
                    validationCriteria: [
                        'window.OTA.adapters.BaseManagerAdapter存在',
                        '可以创建适配器实例',
                        'log方法正常工作',
                        'emit方法正常工作'
                    ],
                    rollbackPlan: '无需回滚，仅验证步骤'
                },

                // 阶段1：OTAManager装饰器验证
                {
                    id: 'step-002',
                    name: 'OTAManager装饰器验证',
                    description: '验证OTAManager装饰器功能正常',
                    type: 'validation',
                    riskLevel: 'low',
                    estimatedTime: 10,
                    prerequisites: ['step-001'],
                    actions: [
                        '验证装饰器加载',
                        '创建模拟OTAManager',
                        '测试装饰器包装',
                        '验证init方法',
                        '验证initialize方法兼容性'
                    ],
                    validationCriteria: [
                        'window.OTA.decorators.OTAManagerDecorator存在',
                        '可以包装OTAManager实例',
                        'init方法正常工作',
                        'initialize方法保持兼容'
                    ],
                    rollbackPlan: '无需回滚，仅验证步骤'
                },

                // 阶段2：OTAManager集成准备
                {
                    id: 'step-003',
                    name: 'OTAManager集成准备',
                    description: '准备OTAManager集成到ApplicationBootstrap',
                    type: 'preparation',
                    riskLevel: 'medium',
                    estimatedTime: 15,
                    prerequisites: ['step-002'],
                    actions: [
                        '创建OTAManager工厂函数',
                        '准备DependencyContainer注册',
                        '验证ApplicationBootstrap兼容性',
                        '创建集成测试'
                    ],
                    validationCriteria: [
                        'OTAManager工厂函数可用',
                        'DependencyContainer可以注册OTAManager',
                        'ApplicationBootstrap可以初始化OTAManager'
                    ],
                    rollbackPlan: '移除工厂函数注册'
                },

                // 阶段2：ApplicationBootstrap集成
                {
                    id: 'step-004',
                    name: 'ApplicationBootstrap集成',
                    description: '将OTAManager集成到ApplicationBootstrap',
                    type: 'integration',
                    riskLevel: 'medium',
                    estimatedTime: 20,
                    prerequisites: ['step-003'],
                    actions: [
                        '修改ApplicationBootstrap managers数组',
                        '注册OTAManager到DependencyContainer',
                        '测试启动流程',
                        '验证Manager初始化'
                    ],
                    validationCriteria: [
                        'ApplicationBootstrap包含otaManager',
                        'OTAManager可以正常初始化',
                        '其他Manager不受影响',
                        '系统启动正常'
                    ],
                    rollbackPlan: '从managers数组移除otaManager，移除DependencyContainer注册'
                },

                // 阶段3：影子系统构建准备
                {
                    id: 'step-005',
                    name: '影子系统构建准备',
                    description: '准备Fliggy处理影子系统',
                    type: 'preparation',
                    riskLevel: 'low',
                    estimatedTime: 30,
                    prerequisites: ['step-004'],
                    actions: [
                        '分析现有Fliggy逻辑',
                        '设计影子系统架构',
                        '创建影子系统基础框架',
                        '准备特性开关配置'
                    ],
                    validationCriteria: [
                        '影子系统框架创建完成',
                        '特性开关配置就绪',
                        '不影响现有Fliggy逻辑'
                    ],
                    rollbackPlan: '删除影子系统文件，重置特性开关'
                },

                // 阶段3：Fliggy影子系统实施
                {
                    id: 'step-006',
                    name: 'Fliggy影子系统实施',
                    description: '实施Fliggy处理影子系统',
                    type: 'implementation',
                    riskLevel: 'medium',
                    estimatedTime: 45,
                    prerequisites: ['step-005'],
                    actions: [
                        '实现影子Fliggy处理器',
                        '集成到影子部署管理器',
                        '配置流量分割',
                        '启动并行运行测试'
                    ],
                    validationCriteria: [
                        '影子系统可以处理Fliggy订单',
                        '处理结果与原系统一致',
                        '不影响生产流程',
                        '可以通过特性开关控制'
                    ],
                    rollbackPlan: '禁用影子系统特性开关，停止并行运行'
                },

                // 阶段4：渐进式切换
                {
                    id: 'step-007',
                    name: '渐进式切换',
                    description: '渐进式切换到新系统',
                    type: 'migration',
                    riskLevel: 'high',
                    estimatedTime: 30,
                    prerequisites: ['step-006'],
                    actions: [
                        '启用10%流量切换',
                        '监控系统表现',
                        '逐步增加到50%',
                        '最终切换到100%'
                    ],
                    validationCriteria: [
                        '新系统处理结果正确',
                        '系统性能稳定',
                        '错误率在可接受范围',
                        '用户体验无影响'
                    ],
                    rollbackPlan: '立即回滚到0%流量，启用紧急回滚'
                },

                // 阶段4：最终验证
                {
                    id: 'step-008',
                    name: '最终验证',
                    description: '验证整个改进方案的成功',
                    type: 'validation',
                    riskLevel: 'low',
                    estimatedTime: 15,
                    prerequisites: ['step-007'],
                    actions: [
                        '全面功能测试',
                        '性能基准对比',
                        '错误率统计',
                        '用户反馈收集'
                    ],
                    validationCriteria: [
                        '所有功能正常工作',
                        '性能不低于原系统',
                        '错误率降低或持平',
                        '代码质量提升'
                    ],
                    rollbackPlan: '如验证失败，执行完整回滚'
                }
            ];
        }

        /**
         * 获取当前步骤
         * @returns {Object|null} 当前步骤
         */
        getCurrentStep() {
            if (this.currentStep < this.improvementPlan.length) {
                return this.improvementPlan[this.currentStep];
            }
            return null;
        }

        /**
         * 获取下一步骤
         * @returns {Object|null} 下一步骤
         */
        getNextStep() {
            if (this.currentStep + 1 < this.improvementPlan.length) {
                return this.improvementPlan[this.currentStep + 1];
            }
            return null;
        }

        /**
         * 验证步骤前置条件
         * @param {Object} step - 步骤对象
         * @returns {boolean} 前置条件是否满足
         */
        validatePrerequisites(step) {
            return step.prerequisites.every(prereqId => {
                const prereqStep = this.improvementPlan.find(s => s.id === prereqId);
                if (!prereqStep) return false;
                
                const execution = this.executionHistory.find(e => e.stepId === prereqId);
                return execution && execution.success;
            });
        }

        /**
         * 执行步骤
         * @param {string} stepId - 步骤ID
         * @returns {Promise<boolean>} 执行是否成功
         */
        async executeStep(stepId) {
            const step = this.improvementPlan.find(s => s.id === stepId);
            if (!step) {
                throw new Error(`步骤不存在: ${stepId}`);
            }

            // 验证前置条件
            if (!this.validatePrerequisites(step)) {
                throw new Error(`步骤前置条件不满足: ${stepId}`);
            }

            this.logger.log(`开始执行步骤: ${step.name} (${stepId})`);

            const execution = {
                stepId,
                startTime: Date.now(),
                endTime: null,
                success: false,
                validationResults: [],
                error: null
            };

            try {
                // 创建快照（如果是高风险步骤）
                if (step.riskLevel === 'high' || step.riskLevel === 'medium') {
                    if (this.hotRollback) {
                        this.hotRollback.createSnapshot(`before-${stepId}`, `执行${step.name}前的快照`);
                    }
                }

                // 执行验证标准
                const validationResults = await this.validateStep(step);
                execution.validationResults = validationResults;

                // 检查是否所有验证都通过
                const allPassed = validationResults.every(result => result.passed);
                
                if (allPassed) {
                    execution.success = true;
                    this.logger.log(`步骤执行成功: ${step.name}`);
                    
                    // 更新当前步骤
                    const stepIndex = this.improvementPlan.findIndex(s => s.id === stepId);
                    if (stepIndex >= this.currentStep) {
                        this.currentStep = stepIndex + 1;
                    }
                } else {
                    throw new Error('步骤验证失败');
                }

            } catch (error) {
                execution.error = error.message;
                this.logger.log(`步骤执行失败: ${step.name} - ${error.message}`, 'error');
                
                // 如果是高风险步骤，考虑自动回滚
                if (step.riskLevel === 'high' && this.hotRollback) {
                    this.logger.log('高风险步骤失败，考虑自动回滚', 'warn');
                }
            } finally {
                execution.endTime = Date.now();
                this.executionHistory.push(execution);
            }

            return execution.success;
        }

        /**
         * 验证步骤
         * @param {Object} step - 步骤对象
         * @returns {Promise<Array>} 验证结果数组
         */
        async validateStep(step) {
            const results = [];

            for (const criterion of step.validationCriteria) {
                const result = {
                    criterion,
                    passed: false,
                    message: '',
                    timestamp: Date.now()
                };

                try {
                    result.passed = await this.evaluateCriterion(criterion);
                    result.message = result.passed ? '验证通过' : '验证失败';
                } catch (error) {
                    result.passed = false;
                    result.message = `验证错误: ${error.message}`;
                }

                results.push(result);
            }

            return results;
        }

        /**
         * 评估验证标准
         * @param {string} criterion - 验证标准
         * @returns {Promise<boolean>} 是否通过
         */
        async evaluateCriterion(criterion) {
            // 这里实现具体的验证逻辑
            switch (true) {
                case criterion.includes('window.OTA.adapters.BaseManagerAdapter存在'):
                    return !!(window.OTA && window.OTA.adapters && window.OTA.adapters.BaseManagerAdapter);
                
                case criterion.includes('window.OTA.decorators.OTAManagerDecorator存在'):
                    return !!(window.OTA && window.OTA.decorators && window.OTA.decorators.OTAManagerDecorator);
                
                case criterion.includes('可以创建适配器实例'):
                    try {
                        const adapter = new window.OTA.adapters.BaseManagerAdapter('test');
                        return !!adapter;
                    } catch {
                        return false;
                    }
                
                case criterion.includes('log方法正常工作'):
                    try {
                        const adapter = new window.OTA.adapters.BaseManagerAdapter('test');
                        adapter.log('test message');
                        return true;
                    } catch {
                        return false;
                    }
                
                default:
                    // 默认返回true，表示需要手动验证
                    return true;
            }
        }

        /**
         * 获取执行进度
         * @returns {Object} 进度信息
         */
        getProgress() {
            const totalSteps = this.improvementPlan.length;
            const completedSteps = this.executionHistory.filter(e => e.success).length;
            const failedSteps = this.executionHistory.filter(e => !e.success).length;

            return {
                total: totalSteps,
                completed: completedSteps,
                failed: failedSteps,
                current: this.currentStep,
                percentage: Math.round((completedSteps / totalSteps) * 100),
                currentStepName: this.getCurrentStep()?.name || 'All steps completed'
            };
        }

        /**
         * 获取执行历史
         * @returns {Array} 执行历史
         */
        getExecutionHistory() {
            return [...this.executionHistory];
        }

        /**
         * 重置执行状态
         */
        reset() {
            this.currentStep = 0;
            this.executionHistory = [];
            this.logger.log('执行状态已重置');
        }

        /**
         * 获取Logger实例
         * @returns {Object} Logger实例
         */
        getLogger() {
            try {
                if (typeof getLogger === 'function') {
                    return getLogger();
                }
                return {
                    log: (message, level = 'info', data = null) => {
                        console.log(`[ProgressivePlanner][${level.toUpperCase()}] ${message}`, data || '');
                    }
                };
            } catch (error) {
                return {
                    log: (message, level = 'info', data = null) => {
                        console.log(`[ProgressivePlanner][${level.toUpperCase()}] ${message}`, data || '');
                    }
                };
            }
        }
    }

    // 创建全局实例
    const progressivePlanner = new ProgressiveImprovementPlanner();

    // 暴露到OTA命名空间
    window.OTA.core.ProgressiveImprovementPlanner = ProgressiveImprovementPlanner;
    window.OTA.progressivePlanner = progressivePlanner;

    console.log('✅ 渐进式改进路径规划器已加载');

})();
