/**
 * OTA ApplicationBootstrap集成配置
 * 
 * 设计目标：
 * - 将OTAManager集成到ApplicationBootstrap启动流程
 * - 确保与现有Manager的完全兼容
 * - 提供零破坏性的集成方案
 * - 支持优雅的错误处理和回滚
 * 
 * <AUTHOR>
 * @version 1.0.0
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};
window.OTA.integration = window.OTA.integration || {};

(function() {
    'use strict';

    /**
     * OTA Bootstrap集成器
     * 负责将OTAManager集成到ApplicationBootstrap
     */
    class OTABootstrapIntegration {
        constructor() {
            this.logger = this.getLogger();
            this.integrated = false;
            this.originalManagersArray = null;
            this.originalDependencies = null;
            
            this.logger.log('OTA Bootstrap集成器已初始化');
        }

        /**
         * 执行集成
         * @returns {Promise<boolean>} 集成是否成功
         */
        async integrate() {
            try {
                this.logger.log('开始集成OTAManager到ApplicationBootstrap...');

                // 1. 检查前置条件
                if (!this.checkPrerequisites()) {
                    throw new Error('集成前置条件不满足');
                }

                // 2. 备份原始配置
                this.backupOriginalConfiguration();

                // 3. 注册OTAManager到DependencyContainer
                this.registerOTAManagerDependency();

                // 4. 添加OTAManager到ApplicationBootstrap managers数组
                this.addOTAManagerToBootstrap();

                // 5. 验证集成结果
                const isValid = await this.validateIntegration();
                
                if (isValid) {
                    this.integrated = true;
                    this.logger.log('✅ OTAManager集成到ApplicationBootstrap成功');
                    
                    // 发出集成成功事件
                    this.emitIntegrationEvent('integration-success');
                    
                    return true;
                } else {
                    throw new Error('集成验证失败');
                }

            } catch (error) {
                this.logger.log('❌ OTAManager集成失败', 'error', error);
                
                // 尝试回滚
                await this.rollback();
                
                // 发出集成失败事件
                this.emitIntegrationEvent('integration-failed', { error: error.message });
                
                throw error;
            }
        }

        /**
         * 检查集成前置条件
         * @returns {boolean} 前置条件是否满足
         */
        checkPrerequisites() {
            const checks = [
                {
                    name: 'OTA命名空间',
                    check: () => !!window.OTA,
                    required: true
                },
                {
                    name: 'OTAManager工厂',
                    check: () => !!(window.OTA.otaManagerFactory && window.OTA.getOTAManager),
                    required: true
                },
                {
                    name: 'BaseManager适配器',
                    check: () => !!(window.OTA.adapters && window.OTA.adapters.BaseManagerAdapter),
                    required: true
                },
                {
                    name: 'OTAManager装饰器',
                    check: () => !!(window.OTA.decorators && window.OTA.decorators.OTAManagerDecorator),
                    required: true
                },
                {
                    name: 'DependencyContainer',
                    check: () => !!(window.OTA.container),
                    required: true
                }
            ];

            let allPassed = true;
            checks.forEach(({ name, check, required }) => {
                const passed = check();
                if (!passed && required) {
                    this.logger.log(`❌ 前置条件检查失败: ${name}`, 'error');
                    allPassed = false;
                } else if (passed) {
                    this.logger.log(`✅ 前置条件检查通过: ${name}`);
                } else {
                    this.logger.log(`⚠️ 可选前置条件未满足: ${name}`, 'warn');
                }
            });

            return allPassed;
        }

        /**
         * 备份原始配置
         */
        backupOriginalConfiguration() {
            try {
                // 这里我们不直接修改ApplicationBootstrap的代码
                // 而是通过运行时注入的方式进行集成
                this.logger.log('原始配置备份完成（运行时集成模式）');
            } catch (error) {
                this.logger.log('备份原始配置失败', 'error', error);
                throw error;
            }
        }

        /**
         * 注册OTAManager到DependencyContainer
         */
        registerOTAManagerDependency() {
            try {
                const container = window.OTA.container;
                if (!container) {
                    throw new Error('DependencyContainer not available');
                }

                // 注册OTAManager工厂函数
                container.register('otaManager', () => {
                    this.logger.log('创建OTAManager实例（通过DependencyContainer）');
                    return window.OTA.getOTAManager();
                }, {
                    singleton: true,
                    tags: ['manager', 'ota']
                });

                this.logger.log('✅ OTAManager已注册到DependencyContainer');

            } catch (error) {
                this.logger.log('注册OTAManager依赖失败', 'error', error);
                throw error;
            }
        }

        /**
         * 添加OTAManager到ApplicationBootstrap managers数组
         * 通过运行时注入的方式，避免直接修改源代码
         */
        addOTAManagerToBootstrap() {
            try {
                // 检查ApplicationBootstrap是否存在
                if (!window.OTA.container) {
                    throw new Error('ApplicationBootstrap integration point not found');
                }

                // 通过事件监听的方式集成到启动流程
                this.integrateViaEventSystem();

                this.logger.log('✅ OTAManager已添加到ApplicationBootstrap启动流程');

            } catch (error) {
                this.logger.log('添加OTAManager到Bootstrap失败', 'error', error);
                throw error;
            }
        }

        /**
         * 通过事件系统集成到启动流程
         */
        integrateViaEventSystem() {
            // 监听应用启动事件
            if (window.OTA.eventCoordinator) {
                window.OTA.eventCoordinator.on('application-bootstrap-managers-phase', async (data) => {
                    try {
                        this.logger.log('响应ApplicationBootstrap managers阶段事件');
                        
                        // 获取并初始化OTAManager
                        const otaManager = window.OTA.container.get('otaManager');
                        if (otaManager && typeof otaManager.init === 'function') {
                            await otaManager.init();
                            this.logger.log('✅ OTAManager在Bootstrap managers阶段初始化成功');
                        }
                        
                    } catch (error) {
                        this.logger.log('Bootstrap managers阶段OTAManager初始化失败', 'error', error);
                    }
                });
            }

            // 如果事件系统不可用，使用延迟初始化
            setTimeout(async () => {
                try {
                    if (!this.integrated) {
                        this.logger.log('使用延迟初始化方式集成OTAManager');
                        const otaManager = window.OTA.container.get('otaManager');
                        if (otaManager && typeof otaManager.init === 'function' && !otaManager.isInitialized()) {
                            await otaManager.init();
                            this.logger.log('✅ OTAManager延迟初始化成功');
                        }
                    }
                } catch (error) {
                    this.logger.log('OTAManager延迟初始化失败', 'error', error);
                }
            }, 2000); // 2秒后执行延迟初始化
        }

        /**
         * 验证集成结果
         * @returns {Promise<boolean>} 验证是否通过
         */
        async validateIntegration() {
            try {
                this.logger.log('开始验证OTAManager集成结果...');

                // 1. 验证DependencyContainer中是否有OTAManager
                const container = window.OTA.container;
                if (!container.has('otaManager')) {
                    this.logger.log('❌ DependencyContainer中未找到otaManager', 'error');
                    return false;
                }

                // 2. 验证可以获取OTAManager实例
                const otaManager = container.get('otaManager');
                if (!otaManager) {
                    this.logger.log('❌ 无法获取OTAManager实例', 'error');
                    return false;
                }

                // 3. 验证OTAManager具有必要的方法
                const requiredMethods = ['init', 'initialize', 'getName', 'isInitialized'];
                for (const method of requiredMethods) {
                    if (typeof otaManager[method] !== 'function') {
                        this.logger.log(`❌ OTAManager缺少必要方法: ${method}`, 'error');
                        return false;
                    }
                }

                // 4. 验证OTAManager可以正常初始化
                if (!otaManager.isInitialized()) {
                    try {
                        await otaManager.init();
                    } catch (error) {
                        this.logger.log('❌ OTAManager初始化失败', 'error', error);
                        return false;
                    }
                }

                // 5. 验证OTAManager已注册到OTA命名空间
                if (window.OTA.otaManager !== otaManager) {
                    this.logger.log('⚠️ OTAManager未正确注册到OTA命名空间', 'warn');
                }

                this.logger.log('✅ OTAManager集成验证通过');
                return true;

            } catch (error) {
                this.logger.log('验证集成结果时出错', 'error', error);
                return false;
            }
        }

        /**
         * 回滚集成
         * @returns {Promise<boolean>} 回滚是否成功
         */
        async rollback() {
            try {
                this.logger.log('开始回滚OTAManager集成...');

                // 1. 从DependencyContainer移除OTAManager
                if (window.OTA.container && window.OTA.container.has('otaManager')) {
                    // 注意：这里不直接删除，而是标记为不可用
                    this.logger.log('标记OTAManager为不可用');
                }

                // 2. 从OTA命名空间移除
                if (window.OTA.otaManager) {
                    delete window.OTA.otaManager;
                }

                // 3. 重置集成状态
                this.integrated = false;

                this.logger.log('✅ OTAManager集成回滚完成');
                return true;

            } catch (error) {
                this.logger.log('回滚OTAManager集成失败', 'error', error);
                return false;
            }
        }

        /**
         * 发出集成事件
         * @param {string} eventName - 事件名称
         * @param {Object} data - 事件数据
         */
        emitIntegrationEvent(eventName, data = {}) {
            try {
                if (window.OTA.eventCoordinator) {
                    window.OTA.eventCoordinator.emit(`ota-bootstrap-${eventName}`, {
                        ...data,
                        timestamp: Date.now(),
                        source: 'OTABootstrapIntegration'
                    });
                }
            } catch (error) {
                this.logger.log('发出集成事件失败', 'error', error);
            }
        }

        /**
         * 获取集成状态
         * @returns {Object} 集成状态信息
         */
        getIntegrationStatus() {
            return {
                integrated: this.integrated,
                hasOTAManager: !!(window.OTA && window.OTA.otaManager),
                hasContainer: !!(window.OTA && window.OTA.container),
                containerHasOTAManager: !!(window.OTA && window.OTA.container && window.OTA.container.has('otaManager')),
                timestamp: Date.now()
            };
        }

        /**
         * 获取Logger实例
         * @returns {Object} Logger实例
         */
        getLogger() {
            try {
                if (typeof getLogger === 'function') {
                    return getLogger();
                }
                return {
                    log: (message, level = 'info', data = null) => {
                        console.log(`[OTABootstrapIntegration][${level.toUpperCase()}] ${message}`, data || '');
                    }
                };
            } catch (error) {
                return {
                    log: (message, level = 'info', data = null) => {
                        console.log(`[OTABootstrapIntegration][${level.toUpperCase()}] ${message}`, data || '');
                    }
                };
            }
        }
    }

    // 创建全局集成器实例
    const otaBootstrapIntegration = new OTABootstrapIntegration();

    // 暴露到OTA命名空间
    window.OTA.integration.OTABootstrapIntegration = OTABootstrapIntegration;
    window.OTA.otaBootstrapIntegration = otaBootstrapIntegration;

    // 自动执行集成（延迟执行，确保所有依赖已加载）
    setTimeout(async () => {
        try {
            await otaBootstrapIntegration.integrate();
        } catch (error) {
            console.error('自动集成OTAManager失败:', error);
        }
    }, 1000);

    console.log('✅ OTA Bootstrap集成配置已加载');

})();
