/**
 * 简化的OTA管理器 - 减法开发方案
 * 
 * 设计原则：
 * - 移除所有不必要的抽象层
 * - 直接继承BaseManager，无需适配器
 * - 简单的策略注册，无需复杂的工厂模式
 * - 直接的事件处理，无需事件桥接器
 * 
 * @version 2.0.0 - 减法重构版
 */

class SimpleOTAManager extends BaseManager {
    constructor() {
        super('SimpleOTAManager');
        
        // 简单的状态管理
        this.strategies = new Map();
        this.currentStrategy = null;
        this.currentChannel = null;
        
        this.log('SimpleOTAManager created');
    }

    /**
     * 初始化 - 只做必要的事情
     */
    async init() {
        try {
            this.log('Initializing SimpleOTAManager...');
            
            // 注册可用的策略
            this.registerAvailableStrategies();
            
            // 设置默认策略
            this.setDefaultStrategy();
            
            this.log('SimpleOTAManager initialized successfully');
            this.emit('ota-manager-ready');
            
        } catch (error) {
            this.error('Failed to initialize SimpleOTAManager:', error);
            throw error;
        }
    }

    /**
     * 注册可用策略 - 简单直接
     */
    registerAvailableStrategies() {
        // 只注册实际存在的策略
        if (typeof FliggyOTAStrategy !== 'undefined') {
            this.strategies.set('fliggy', new FliggyOTAStrategy());
            this.log('Fliggy strategy registered');
        }
        
        if (typeof JingGeOTAStrategy !== 'undefined') {
            this.strategies.set('jingge', new JingGeOTAStrategy());
            this.log('JingGe strategy registered');
        }
        
        // 默认策略 - 直接内联，无需外部依赖
        this.strategies.set('default', {
            name: 'default',
            getFieldPromptSnippets: () => ({}), // 默认策略不提供特殊提示词片段
            activate: () => {},
            deactivate: () => {}
        });
        
        this.log(`Registered ${this.strategies.size} strategies`);
    }

    /**
     * 设置默认策略
     */
    setDefaultStrategy() {
        this.switchToStrategy('default');
    }

    /**
     * 切换策略 - 简单直接
     */
    switchToStrategy(channel) {
        const strategy = this.strategies.get(channel);
        if (!strategy) {
            this.warn(`No strategy found for channel: ${channel}, using default`);
            return this.switchToStrategy('default');
        }

        // 停用当前策略
        if (this.currentStrategy && this.currentStrategy.deactivate) {
            this.currentStrategy.deactivate();
        }

        // 激活新策略
        this.currentStrategy = strategy;
        this.currentChannel = channel;
        
        if (strategy.activate) {
            strategy.activate();
        }

        this.log(`Switched to strategy: ${channel}`);
        this.emit('strategy-changed', { channel, strategy });
        
        return true;
    }

    /**
     * 获取当前策略的提示词片段 - 核心功能
     * @returns {Object} 字段级提示词片段映射
     */
    getCurrentStrategyPromptSnippets() {
        if (!this.currentStrategy) {
            this.warn('No active strategy, returning empty prompt snippets');
            return {};
        }

        try {
            // 优先使用静态方法（推荐方式）
            const StrategyClass = this.currentStrategy.constructor;
            if (typeof StrategyClass.getFieldPromptSnippets === 'function') {
                return StrategyClass.getFieldPromptSnippets();
            }

            // 回退到实例方法
            if (typeof this.currentStrategy.getFieldPromptSnippets === 'function') {
                return this.currentStrategy.getFieldPromptSnippets();
            }

            return {};
        } catch (error) {
            this.error('Failed to get strategy prompt snippets:', error);
            return {};
        }
    }

    /**
     * 处理渠道检测结果 - 直接简单
     */
    handleChannelDetection(result) {
        const { channel, confidence } = result;

        this.log(`Channel detected: ${channel} (confidence: ${confidence})`);

        if (this.strategies.has(channel)) {
            this.switchToStrategy(channel);
        } else {
            this.warn(`Unknown channel: ${channel}, keeping current strategy`);
        }
    }

    /**
     * 获取当前激活的渠道名称
     * @returns {string|null} 当前渠道名称
     */
    getCurrentChannelName() {
        return this.currentChannel;
    }

    /**
     * 获取状态信息
     */
    getStatus() {
        return {
            name: this.name,
            initialized: this.initialized,
            currentChannel: this.currentChannel,
            currentStrategy: this.currentStrategy?.name || 'none',
            availableStrategies: Array.from(this.strategies.keys()),
            strategyCount: this.strategies.size
        };
    }

    /**
     * 销毁
     */
    destroy() {
        this.log('Destroying SimpleOTAManager...');
        
        // 停用当前策略
        if (this.currentStrategy && this.currentStrategy.deactivate) {
            this.currentStrategy.deactivate();
        }
        
        // 清理
        this.strategies.clear();
        this.currentStrategy = null;
        this.currentChannel = null;
        
        this.log('SimpleOTAManager destroyed');
    }
}

// 创建全局实例
const simpleOTAManagerInstance = new SimpleOTAManager();

// 暴露类和实例
window.SimpleOTAManager = SimpleOTAManager;
window.simpleOTAManager = simpleOTAManagerInstance;

// 如果要替换现有的复杂系统，可以这样做：
if (window.OTA) {
    window.OTA.SimpleOTAManager = simpleOTAManagerInstance; // 注意：这里暴露实例，不是类
    window.OTA.simpleOTAManager = simpleOTAManagerInstance;
}

// 自动初始化实例
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        simpleOTAManagerInstance.init();
    });
} else {
    simpleOTAManagerInstance.init();
}

console.log('✅ SimpleOTAManager loaded - 减法开发版本');
