/**
 * OTA通道管理器 - 架构调和方案实现
 * 
 * 设计理念：
 * - 外部保持Manager模式接口一致性
 * - 内部使用策略模式实现OTA通道隔离
 * - 与现有架构无缝集成
 * 
 * <AUTHOR>
 * @version 1.0.0
 */

class OTAManager extends BaseManager {
    constructor() {
        super('OTAManager');
        
        // 策略注册表
        this.strategyRegistry = new Map();
        
        // 当前活动策略
        this.activeStrategy = null;
        this.currentChannel = null;
        
        // 配置管理器
        this.configManager = null;
        
        // 事件桥接器
        this.eventBridge = null;
        
        // 初始化状态
        this.initialized = false;
        
        this.log('OTAManager created');
    }

    /**
     * 初始化管理器
     * 在ApplicationBootstrap的managers阶段调用
     */
    async initialize() {
        try {
            this.log('Initializing OTAManager...');
            
            // 1. 初始化配置管理器
            this.configManager = new OTAConfigurationManager();
            await this.configManager.initialize();
            
            // 2. 初始化事件桥接器
            this.eventBridge = new OTAEventBridge(this);
            this.eventBridge.setupEventHandlers();
            
            // 3. 注册默认策略
            this.registerDefaultStrategies();
            
            // 4. 设置默认策略
            this.setDefaultStrategy();
            
            this.initialized = true;
            this.log('OTAManager initialized successfully');
            
            // 发出初始化完成事件
            this.emit('ota-manager-initialized');
            
        } catch (error) {
            this.error('Failed to initialize OTAManager:', error);
            throw error;
        }
    }

    /**
     * 注册OTA策略
     * @param {string} channel - OTA通道名称
     * @param {BaseOTAStrategy} strategy - 策略实例
     */
    registerStrategy(channel, strategy) {
        if (!channel || !strategy) {
            throw new Error('Channel and strategy are required');
        }

        if (this.strategyRegistry.has(channel)) {
            this.warn(`Strategy for channel ${channel} already exists, overriding...`);
        }

        // 设置策略的配置和事件处理
        strategy.setConfigManager(this.configManager);
        strategy.setEventEmitter(this.eventBridge);
        
        this.strategyRegistry.set(channel, strategy);
        this.log(`Strategy registered for channel: ${channel}`);
        
        // 发出策略注册事件
        this.emit('strategy-registered', { channel, strategy });
    }

    /**
     * 获取指定通道的策略
     * @param {string} channel - OTA通道名称
     * @returns {BaseOTAStrategy|null}
     */
    getStrategy(channel) {
        return this.strategyRegistry.get(channel) || null;
    }

    /**
     * 处理通道检测结果
     * 这是与现有系统集成的主要入口点
     * @param {Object} detectionResult - 通道检测结果
     */
    handleChannelDetection(detectionResult) {
        const { channel, confidence, metadata } = detectionResult;
        
        this.log(`Channel detected: ${channel} (confidence: ${confidence})`);
        
        try {
            // 切换到对应策略
            if (this.switchToStrategy(channel, metadata)) {
                // 发出通道切换事件
                this.emit('channel-switched', {
                    from: this.currentChannel,
                    to: channel,
                    strategy: this.activeStrategy,
                    metadata
                });
                
                this.currentChannel = channel;
            }
        } catch (error) {
            this.error(`Failed to switch to strategy for channel ${channel}:`, error);
            this.switchToDefaultStrategy();
        }
    }

    /**
     * 切换到指定策略
     * @param {string} channel - 目标通道
     * @param {Object} metadata - 附加元数据
     * @returns {boolean} 切换是否成功
     */
    switchToStrategy(channel, metadata = {}) {
        const strategy = this.getStrategy(channel);
        
        if (!strategy) {
            this.warn(`No strategy found for channel: ${channel}, using default`);
            return this.switchToDefaultStrategy();
        }

        try {
            // 停用当前策略
            if (this.activeStrategy && this.activeStrategy !== strategy) {
                this.activeStrategy.deactivate();
            }

            // 激活新策略
            strategy.activate(metadata);
            this.activeStrategy = strategy;
            
            this.log(`Switched to strategy for channel: ${channel}`);
            return true;
            
        } catch (error) {
            this.error(`Failed to activate strategy for channel ${channel}:`, error);
            return false;
        }
    }

    /**
     * 切换到默认策略
     * @returns {boolean} 切换是否成功
     */
    switchToDefaultStrategy() {
        return this.switchToStrategy('default');
    }

    /**
     * 获取当前激活策略的提示词片段
     * 策略的核心职责：为AI提供渠道特定的提示词片段
     * @returns {Object} 字段级提示词片段映射
     */
    getCurrentStrategyPromptSnippets() {
        if (!this.activeStrategy) {
            this.warn('No active strategy, returning empty prompt snippets');
            return {};
        }

        try {
            // 优先使用静态方法（推荐方式）
            const StrategyClass = this.activeStrategy.constructor;
            if (typeof StrategyClass.getFieldPromptSnippets === 'function') {
                return StrategyClass.getFieldPromptSnippets();
            }

            // 回退到实例方法
            if (typeof this.activeStrategy.getFieldPromptSnippets === 'function') {
                return this.activeStrategy.getFieldPromptSnippets();
            }

            return {};
        } catch (error) {
            this.error('Failed to get strategy prompt snippets:', error);
            return {};
        }
    }

    /**
     * 获取当前通道配置
     * @returns {Object} 当前配置
     */
    getCurrentConfiguration() {
        if (!this.activeStrategy) {
            return this.configManager.getDefaultConfiguration();
        }
        
        return this.activeStrategy.getConfiguration();
    }

    /**
     * 更新配置
     * @param {Object} config - 新配置
     */
    updateConfiguration(config) {
        if (this.activeStrategy) {
            this.activeStrategy.updateConfiguration(config);
        }
        
        this.emit('configuration-updated', { config });
    }

    /**
     * 注册默认策略
     * @private
     */
    registerDefaultStrategies() {
        try {
            // 检查策略类是否可用
            const availableStrategies = [];

            // 注册默认策略
            if (typeof DefaultOTAStrategy !== 'undefined') {
                this.registerStrategy('default', new DefaultOTAStrategy());
                availableStrategies.push('default');
            } else {
                this.warn('DefaultOTAStrategy not available, creating fallback');
                this.registerStrategy('default', this.createFallbackStrategy());
                availableStrategies.push('default (fallback)');
            }
            
            // 注册Fliggy特供策略
            if (typeof FliggyOTAStrategy !== 'undefined') {
                this.registerStrategy('fliggy', new FliggyOTAStrategy());
                availableStrategies.push('fliggy');
            } else {
                this.warn('FliggyOTAStrategy not available');
            }
            
            // 注册其他已知策略（如果可用）
            if (typeof BookingOTAStrategy !== 'undefined') {
                this.registerStrategy('booking', new BookingOTAStrategy());
                availableStrategies.push('booking');
            }
            
            if (typeof ExpediaOTAStrategy !== 'undefined') {
                this.registerStrategy('expedia', new ExpediaOTAStrategy());
                availableStrategies.push('expedia');
            }
            
            this.log(`Default strategies registered: ${availableStrategies.join(', ')}`);
            
        } catch (error) {
            this.error('Failed to register default strategies:', error);
            // 确保至少有一个回退策略
            if (!this.getStrategy('default')) {
                this.registerStrategy('default', this.createFallbackStrategy());
                this.log('Fallback strategy registered as default');
            }
        }
    }

    /**
     * 创建回退策略
     * @returns {Object} 回退策略实例
     * @private
     */
    createFallbackStrategy() {
        return {
            activate: () => {},
            deactivate: () => {},
            processFormData: (data) => data,
            getConfiguration: () => ({}),
            updateConfiguration: () => {},
            setConfigManager: () => {},
            setEventEmitter: () => {}
        };
    }

    /**
     * 设置默认策略
     * @private
     */
    setDefaultStrategy() {
        this.switchToDefaultStrategy();
    }

    /**
     * 获取当前激活的渠道名称
     * @returns {string|null} 当前渠道名称
     */
    getCurrentChannelName() {
        return this.currentChannel;
    }

    /**
     * 获取管理器状态信息
     * @returns {Object} 状态信息
     */
    getStatus() {
        return {
            initialized: this.initialized,
            currentChannel: this.currentChannel,
            activeStrategy: this.activeStrategy?.constructor.name || null,
            registeredStrategies: Array.from(this.strategyRegistry.keys()),
            strategyCount: this.strategyRegistry.size
        };
    }

    /**
     * 销毁管理器
     */
    destroy() {
        this.log('Destroying OTAManager...');
        
        // 停用当前策略
        if (this.activeStrategy) {
            this.activeStrategy.deactivate();
        }
        
        // 清理事件桥接器
        if (this.eventBridge) {
            this.eventBridge.cleanup();
        }
        
        // 清理配置管理器
        if (this.configManager) {
            this.configManager.cleanup();
        }
        
        // 清理策略注册表
        this.strategyRegistry.clear();
        
        // 调用父类销毁方法
        super.destroy();
        
        this.log('OTAManager destroyed');
    }
}

// 导出给依赖注入系统使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = OTAManager;
} else if (typeof window !== 'undefined') {
    window.OTAManager = OTAManager;
}
