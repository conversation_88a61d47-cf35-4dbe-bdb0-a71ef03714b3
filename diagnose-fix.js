/**
 * 渠道检测修复诊断脚本
 * 在浏览器控制台中运行此脚本来验证修复效果
 */

console.log('🔧 开始渠道检测修复验证...\n');

const testOrderData = `订单编号：2870626119501574181买家：芦荟的斑点支付时间：2025-08-09 19:26:46
查看详情

豪华7座

【接机】

新加坡-新加坡

[出发]樟宜机场T4

[抵达]新加坡滨海宾乐雅酒店

约22.9公里

HO1601

[预计抵达]

2025-08-10 14:45:00

王湖皎

真实号：15216832557

---
4成人1儿童

司机姓名：kk`;

// 测试1: 验证FliggyOTAStrategy.detectFromContent方法
console.log('=== 测试1: FliggyOTAStrategy.detectFromContent 方法 ===');
if (typeof FliggyOTAStrategy !== 'undefined' && typeof FliggyOTAStrategy.detectFromContent === 'function') {
    try {
        const fliggyResult = FliggyOTAStrategy.detectFromContent(testOrderData);
        console.log('✅ FliggyOTAStrategy.detectFromContent 方法存在');
        console.log('Fliggy检测结果:', fliggyResult);
        
        if (fliggyResult.detectedChannel === 'fliggy' && fliggyResult.confidence > 0.9) {
            console.log('✅ Fliggy渠道检测成功');
        } else {
            console.log('❌ Fliggy渠道检测失败');
        }
    } catch (error) {
        console.log('❌ 方法调用失败:', error.message);
    }
} else {
    console.log('❌ FliggyOTAStrategy.detectFromContent 方法不存在');
}

// 测试2: 验证JingGeOTAStrategy.detectFromContent方法
console.log('\n=== 测试2: JingGeOTAStrategy.detectFromContent 方法 ===');
if (typeof JingGeOTAStrategy !== 'undefined' && typeof JingGeOTAStrategy.detectFromContent === 'function') {
    try {
        const jinggeResult = JingGeOTAStrategy.detectFromContent(testOrderData);
        console.log('✅ JingGeOTAStrategy.detectFromContent 方法存在');
        console.log('JingGe检测结果:', jinggeResult);
    } catch (error) {
        console.log('❌ 方法调用失败:', error.message);
    }
} else {
    console.log('❌ JingGeOTAStrategy.detectFromContent 方法不存在');
}

// 测试3: 验证渠道检测器集成
console.log('\n=== 测试3: 渠道检测器集成 ===');
if (window.OTA && window.OTA.channelDetector) {
    try {
        const detectionResult = window.OTA.channelDetector.detectChannel(testOrderData, '2870626119501574181');
        console.log('✅ 渠道检测器可用');
        console.log('检测结果:', detectionResult);
        
        if (detectionResult.detectedChannel === 'fliggy') {
            console.log('✅ 集成渠道检测成功');
        } else {
            console.log('❌ 集成渠道检测失败，检测到的渠道:', detectionResult.detectedChannel);
        }
    } catch (error) {
        console.log('❌ 渠道检测器调用失败:', error.message);
    }
} else {
    console.log('❌ 渠道检测器不可用');
}

// 测试4: 验证策略切换
console.log('\n=== 测试4: 策略切换验证 ===');
if (window.OTA && window.OTA.SimpleOTAManager) {
    try {
        console.log('当前策略:', window.OTA.SimpleOTAManager.getCurrentChannelName());
        
        // 尝试切换到Fliggy策略
        const switchResult = window.OTA.SimpleOTAManager.switchToStrategy('fliggy');
        console.log('策略切换结果:', switchResult);
        console.log('切换后策略:', window.OTA.SimpleOTAManager.getCurrentChannelName());
        
        if (window.OTA.SimpleOTAManager.getCurrentChannelName() === 'fliggy') {
            console.log('✅ 策略切换成功');
            
            // 验证提示词片段获取
            const strategy = window.OTA.SimpleOTAManager.strategies.get('fliggy');
            if (strategy && typeof strategy.getFieldPromptSnippets === 'function') {
                const snippets = strategy.getFieldPromptSnippets();
                console.log('提示词片段:', snippets);
                
                if (snippets.ota && snippets.ota_price && snippets.car_type_id) {
                    console.log('✅ 提示词片段获取成功');
                } else {
                    console.log('❌ 提示词片段不完整');
                }
            } else {
                console.log('❌ 策略实例方法不可用');
            }
        } else {
            console.log('❌ 策略切换失败');
        }
    } catch (error) {
        console.log('❌ 策略切换测试失败:', error.message);
    }
} else {
    console.log('❌ SimpleOTAManager不可用');
}

// 测试5: 正则表达式验证
console.log('\n=== 测试5: 正则表达式验证 ===');
const fliggyPattern = /订单编号[：:\s]*\d{19}/g;
const matches = testOrderData.match(fliggyPattern);

console.log('正则表达式:', fliggyPattern.toString());
console.log('匹配结果:', matches);

if (matches && matches.length > 0) {
    console.log('✅ 正则表达式匹配成功');
} else {
    console.log('❌ 正则表达式匹配失败');
}

console.log('\n🎯 修复验证完成！');
console.log('如果所有测试都通过，说明渠道检测和策略切换修复成功。');
