/**
 * 依赖标签（Dependency Tags）
 * 文件: js/core/ota-manager-factory.js
 * 角色: OTA 管理器工厂（按需构造各 Manager/Service 实例）
 * 上游依赖(直接使用): DependencyContainer, Registry
 * 下游被依赖(常见调用方): Bootstrap/UIManager/SystemIntegrator
 * 事件: 无
 * 更新时间: 2025-08-09
 */
/**
 * OTAManager工厂 - 零破坏性OTAManager创建和集成
 * 
 * 设计目标：
 * - 创建经过装饰器增强的OTAManager实例
 * - 处理BaseManager继承问题
 * - 提供ApplicationBootstrap兼容的接口
 * - 确保零破坏性集成
 * 
 * <AUTHOR>
 * @version 1.0.0
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};
window.OTA.factories = window.OTA.factories || {};

(function() {
    'use strict';

    /**
     * OTAManager工厂类
     * 负责创建和配置OTAManager实例
     */
    class OTAManagerFactory {
        constructor() {
            this.logger = this.getLogger();
            this.createdInstances = new Map();
            
            this.logger.log('OTAManager工厂已初始化');
        }

        /**
         * 创建OTAManager实例
         * @param {Object} options - 创建选项
         * @returns {Object} 装饰后的OTAManager实例
         */
        createOTAManager(options = {}) {
            try {
                this.logger.log('开始创建OTAManager实例...');

                // 1. 检查是否已有实例（单例模式）
                if (options.singleton !== false && this.createdInstances.has('default')) {
                    this.logger.log('返回现有的OTAManager单例实例');
                    return this.createdInstances.get('default');
                }

                // 2. 创建模拟的OTAManager实例（避免BaseManager继承问题）
                const mockOTAManager = this.createMockOTAManager();

                // 3. 使用装饰器增强实例
                const decoratedManager = this.decorateOTAManager(mockOTAManager);

                // 4. 配置实例
                this.configureOTAManager(decoratedManager, options);

                // 5. 缓存实例
                if (options.singleton !== false) {
                    this.createdInstances.set('default', decoratedManager);
                }

                this.logger.log('OTAManager实例创建成功');
                return decoratedManager;

            } catch (error) {
                this.logger.log('创建OTAManager实例失败', 'error', error);
                throw error;
            }
        }

        /**
         * 创建模拟的OTAManager实例
         * 避免BaseManager继承问题
         * @returns {Object} 模拟的OTAManager实例
         */
        createMockOTAManager() {
            const mockManager = {
                // 基础属性
                name: 'OTAManager',
                initialized: false,
                
                // 策略相关属性
                strategyRegistry: new Map(),
                activeStrategy: null,
                currentChannel: null,
                
                // 配置和事件
                configManager: null,
                eventBridge: null,

                // 核心方法 - initialize
                async initialize() {
                    try {
                        this.log('Initializing OTAManager...');
                        
                        // 1. 初始化配置管理器（模拟）
                        this.configManager = this.createMockConfigManager();
                        
                        // 2. 初始化事件桥接器（模拟）
                        this.eventBridge = this.createMockEventBridge();
                        
                        // 3. 注册默认策略
                        this.registerDefaultStrategies();
                        
                        // 4. 设置默认策略
                        this.setDefaultStrategy();
                        
                        this.initialized = true;
                        this.log('OTAManager initialized successfully');
                        
                        // 发出初始化完成事件
                        this.emit('ota-manager-initialized');
                        
                    } catch (error) {
                        this.error('Failed to initialize OTAManager:', error);
                        throw error;
                    }
                },

                // 策略管理方法
                registerStrategy(channel, strategy) {
                    this.strategyRegistry.set(channel, strategy);
                    this.log(`Strategy registered for channel: ${channel}`);
                    this.emit('strategy-registered', { channel, strategy });
                },

                switchToStrategy(channel, confidence = 1.0) {
                    try {
                        const strategy = this.strategyRegistry.get(channel);
                        if (strategy) {
                            this.activeStrategy = strategy;
                            this.currentChannel = channel;
                            this.log(`Switched to strategy for channel: ${channel}`);
                            this.emit('channel-switched', {
                                from: this.currentChannel,
                                to: channel,
                                confidence
                            });
                            return true;
                        }
                        return false;
                    } catch (error) {
                        this.error(`Failed to switch to strategy for channel ${channel}:`, error);
                        return false;
                    }
                },

                getCurrentStrategyPromptSnippets() {
                    try {
                        if (!this.activeStrategy) {
                            return {};
                        }

                        // 优先使用静态方法（推荐方式）
                        const StrategyClass = this.activeStrategy.constructor;
                        if (typeof StrategyClass.getFieldPromptSnippets === 'function') {
                            return StrategyClass.getFieldPromptSnippets();
                        }

                        // 回退到实例方法
                        if (typeof this.activeStrategy.getFieldPromptSnippets === 'function') {
                            return this.activeStrategy.getFieldPromptSnippets();
                        }

                        return {};
                    } catch (error) {
                        this.error('Failed to get strategy prompt snippets:', error);
                        return {};
                    }
                },

                // 辅助方法
                registerDefaultStrategies() {
                    // 注册默认策略（模拟）
                    this.registerStrategy('default', { name: 'DefaultStrategy' });
                    this.registerStrategy('fliggy', { name: 'FliggyStrategy' });
                    this.log('Default strategies registered');
                },

                setDefaultStrategy() {
                    this.switchToStrategy('default');
                },

                getCurrentChannelName() {
                    return this.currentChannel || null;
                },

                createMockConfigManager() {
                    return {
                        initialize: async () => {
                            this.log('Mock ConfigManager initialized');
                        }
                    };
                },

                createMockEventBridge() {
                    return {
                        setupEventHandlers: () => {
                            this.log('Mock EventBridge setup completed');
                        }
                    };
                },

                // 生命周期方法
                destroy() {
                    this.log('Destroying OTAManager...');
                    this.strategyRegistry.clear();
                    this.activeStrategy = null;
                    this.currentChannel = null;
                    this.initialized = false;
                    this.log('OTAManager destroyed');
                },

                // 获取器方法
                getName() {
                    return this.name;
                },

                isInitialized() {
                    return this.initialized;
                }
            };

            return mockManager;
        }

        /**
         * 使用装饰器增强OTAManager
         * @param {Object} otaManager - 原始OTAManager实例
         * @returns {Object} 装饰后的实例
         */
        decorateOTAManager(otaManager) {
            if (!window.OTA.decorators || !window.OTA.decorators.OTAManagerDecorator) {
                throw new Error('OTAManagerDecorator not available');
            }

            const decorator = new window.OTA.decorators.OTAManagerDecorator(otaManager);
            this.logger.log('OTAManager已通过装饰器增强');
            
            return decorator;
        }

        /**
         * 配置OTAManager实例
         * @param {Object} manager - OTAManager实例
         * @param {Object} options - 配置选项
         */
        configureOTAManager(manager, options) {
            // 应用配置选项
            if (options.name) {
                manager.name = options.name;
            }

            // 设置调试模式
            if (options.debug) {
                manager.debugMode = true;
            }

            this.logger.log('OTAManager配置完成', 'info', options);
        }

        /**
         * 获取现有实例
         * @param {string} instanceId - 实例ID
         * @returns {Object|null} OTAManager实例
         */
        getInstance(instanceId = 'default') {
            return this.createdInstances.get(instanceId) || null;
        }

        /**
         * 销毁实例
         * @param {string} instanceId - 实例ID
         */
        destroyInstance(instanceId = 'default') {
            const instance = this.createdInstances.get(instanceId);
            if (instance && typeof instance.destroy === 'function') {
                instance.destroy();
            }
            this.createdInstances.delete(instanceId);
            this.logger.log(`OTAManager实例已销毁: ${instanceId}`);
        }

        /**
         * 销毁所有实例
         */
        destroyAllInstances() {
            this.createdInstances.forEach((_instance, id) => {
                this.destroyInstance(id);
            });
            this.logger.log('所有OTAManager实例已销毁');
        }

        /**
         * 获取Logger实例
         * @returns {Object} Logger实例
         */
        getLogger() {
            try {
                if (typeof getLogger === 'function') {
                    return getLogger();
                }
                return {
                    log: (message, level = 'info', data = null) => {
                        console.log(`[OTAManagerFactory][${level.toUpperCase()}] ${message}`, data || '');
                    }
                };
            } catch (error) {
                return {
                    log: (message, level = 'info', data = null) => {
                        console.log(`[OTAManagerFactory][${level.toUpperCase()}] ${message}`, data || '');
                    }
                };
            }
        }
    }

    // 创建全局工厂实例
    const otaManagerFactory = new OTAManagerFactory();

    // 暴露到OTA命名空间
    window.OTA.factories.OTAManagerFactory = OTAManagerFactory;
    window.OTA.otaManagerFactory = otaManagerFactory;

    // 提供便捷的创建函数
    window.OTA.createOTAManager = function(options) {
        return otaManagerFactory.createOTAManager(options);
    };

    // 提供ApplicationBootstrap兼容的工厂函数
    window.OTA.getOTAManager = function() {
        let instance = otaManagerFactory.getInstance();
        if (!instance) {
            instance = otaManagerFactory.createOTAManager({ singleton: true });
        }
        return instance;
    };

    console.log('✅ OTAManager工厂已加载');

})();
