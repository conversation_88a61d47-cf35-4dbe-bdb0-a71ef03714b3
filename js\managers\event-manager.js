/**
 * 依赖标签（Dependency Tags）
 * 文件: js/managers/event-manager.js
 * 角色: 事件管理（注册、派发、解绑），桥接全局与模块事件
 * 上游依赖(直接使用): Lo<PERSON>, GlobalEventCoordinator
 * 下游被依赖(常见调用方): UIManager, MultiOrderManagerV2, 各 Managers/Services
 * 事件: 作为统一事件路由
 * 更新时间: 2025-08-09
 */
/**
 * 事件管理器模块
 * 负责处理用户交互事件、键盘快捷键和各种UI事件
 * 协调不同组件之间的事件通信
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};
window.OTA.managers = window.OTA.managers || {};

(function() {
    'use strict';

    // 获取依赖模块 - 使用统一的服务定位器

    /**
     * 事件管理器类
     * 负责事件处理和用户交互
     */
    class EventManager {
        constructor(elements, uiManager) {
            this.elements = elements;
            this.uiManager = uiManager;
            this.currentModal = null;
            this.logger = getLogger(); // 缓存logger实例
            this.debounceTimers = new Map(); // 防抖计时器集合
            
            // 绑定方法上下文
            this.handleLogin = this.handleLogin.bind(this);
            this.handleLogout = this.handleLogout.bind(this);
            this.handleParseOrder = this.handleParseOrder.bind(this);
            this.handleCreateOrder = this.handleCreateOrder.bind(this);
            this.handleThemeToggle = this.handleThemeToggle.bind(this);
            this.handleKeyboardShortcuts = this.handleKeyboardShortcuts.bind(this);
            this.handleLanguageChange = this.handleLanguageChange.bind(this);
            this.handleShowHistory = this.handleShowHistory.bind(this);
            this.handleReturnToMultiOrder = this.handleReturnToMultiOrder.bind(this);
        }

        /**
         * 初始化事件管理器
         */
        init() {
            this.bindEvents();
            this.setupKeyboardShortcuts();
            this.logger.log('事件管理器初始化完成', 'success');
        }

        bindEvents() {
            this.logger.log('开始绑定事件监听器...', 'info');
            this.bindLoginEvents();
            this.bindCoreButtonEvents();
            this.bindFunctionalButtons();
            this.bindModalEvents();
            this.bindPreviewModalEvents();
            this.bindFieldEditEvents();
            this.logger.log('所有事件监听器绑定完成', 'success');
        }

        bindLoginEvents() {
            if (this.elements.loginForm) {
                this.elements.loginForm.addEventListener('submit', this.handleLogin.bind(this));
            }
            if (this.elements.clearSavedBtn) {
                this.elements.clearSavedBtn.addEventListener('click', () => this.handleClearSaved());
            }
        }

        bindCoreButtonEvents() {
            const historyBtn = document.getElementById('historyBtn');
            if (historyBtn) {
                historyBtn.removeEventListener('click', this.handleShowHistory);
                historyBtn.addEventListener('click', (event) => {
                    event.preventDefault();
                    event.stopPropagation();
                    this.handleShowHistory();
                });
            }

            const logoutBtn = document.getElementById('logoutBtn');
            if (logoutBtn) {
                // 确保清理之前的事件监听器
                logoutBtn.removeEventListener('click', this.handleLogout);
                // 使用绑定的方法引用以确保正确清理
                logoutBtn.addEventListener('click', this.handleLogout);
            }

            const returnToMultiOrderBtn = document.getElementById('returnToMultiOrder');
            if (returnToMultiOrderBtn) {
                returnToMultiOrderBtn.removeEventListener('click', this.handleReturnToMultiOrder);
                returnToMultiOrderBtn.addEventListener('click', (event) => {
                    event.preventDefault();
                    event.stopPropagation();
                    this.handleReturnToMultiOrder();
                });
            }
        }

        bindFunctionalButtons() {
            // 语言切换
            const languageSelect = document.getElementById('languageSelect');
            if (languageSelect) {
                languageSelect.addEventListener('change', this.handleLanguageChange.bind(this));
            }

            // 订单解析按钮
            if (this.elements.parseBtn) {
                this.elements.parseBtn.addEventListener('click', this.handleParseOrder.bind(this));
            }

            // 订单创建按钮 - 添加防抖优化
            if (this.elements.createBtn) {
                // 防抖处理，防止重复点击
                const debouncedCreateOrder = this.debounce(this.handleCreateOrder.bind(this), 1000);
                this.elements.createBtn.addEventListener('click', debouncedCreateOrder);
            }

            // 订单验证按钮（已移除validateDataBtn，功能合并到创建订单中）

            // 重置按钮
            if (this.elements.resetBtn) {
                this.elements.resetBtn.addEventListener('click', () => this.handleResetOrder());
            }

            // 主题切换按钮
            if (this.elements.themeToggle) {
                this.elements.themeToggle.addEventListener('click', this.handleThemeToggle.bind(this));
            }

            // 清除日志按钮
            if (this.elements.clearLogsBtn) {
                this.elements.clearLogsBtn.addEventListener('click', () => this.handleClearLogs());
            }

            // 导出日志按钮
            if (this.elements.exportLogsBtn) {
                this.elements.exportLogsBtn.addEventListener('click', () => this.handleExportLogs());
            }

            // 调试模式切换
            if (this.elements.debugMode) {
                this.elements.debugMode.addEventListener('change', () => this.handleDebugModeChange());
            }

            // 生成示例订单按钮
            const generateSampleBtn = document.getElementById('generateSampleBtn');
            if (generateSampleBtn) {
                generateSampleBtn.addEventListener('click', () => {
                    if (this.elements.orderInput) {
                        this.elements.orderInput.value = getGeminiService().generateSampleOrder();
                    }
                });
            }

            // 清空输入按钮 - 修复ID匹配问题
            const clearInputBtn = document.getElementById('clearInput');
            if (clearInputBtn) {
                clearInputBtn.addEventListener('click', () => this.handleClearInput());
                getLogger().log('清空输入按钮事件已绑定', 'info');
            }

            // 手动多订单检测按钮
            const manualMultiOrderBtn = document.getElementById('manualMultiOrderDetection');
            if (manualMultiOrderBtn) {
                manualMultiOrderBtn.addEventListener('click', () => this.handleManualMultiOrderDetection());
                getLogger().log('手动多订单检测按钮事件已绑定', 'info');
            }
        }

        bindModalEvents() {
            // 模态框关闭事件
            if (this.elements.modalCancel) {
                this.elements.modalCancel.addEventListener('click', () => this.hideModal());
            }
            if (this.elements.modalClose) {
                this.elements.modalClose.addEventListener('click', () => this.hideModal());
            }

            // 点击模态框背景关闭
            if (this.elements.modal) {
                this.elements.modal.addEventListener('click', (e) => {
                    if (e.target === this.elements.modal) {
                        this.hideModal();
                    }
                });
            }
        }

        bindPreviewModalEvents() {
            const closePreviewModal = document.getElementById('closePreviewModal');
            if (closePreviewModal) {
                closePreviewModal.addEventListener('click', () => this.hidePreviewModal());
            }

            const previewModalOverlay = document.getElementById('previewSection');
            if (previewModalOverlay) {
                previewModalOverlay.addEventListener('click', (e) => {
                    if (e.target === previewModalOverlay) {
                        this.hidePreviewModal();
                    }
                });
            }
        }

        bindFieldEditEvents() {
            document.addEventListener('click', (e) => {
                if (e.target.classList.contains('edit-field-btn')) {
                    this.handleFieldEdit(e.target);
                }
            });
        }

        setupKeyboardShortcuts() {
            document.addEventListener('keydown', this.handleKeyboardShortcuts);
        }

        handleKeyboardShortcuts(e) {
            // Ctrl/Cmd + Enter: 手动解析订单
            if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
                if (document.activeElement === this.elements.orderInput) {
                    e.preventDefault();
                    this.handleParseOrder();
                }
            }

            // Escape: 关闭模态框
            if (e.key === 'Escape' && this.currentModal) {
                this.hideModal();
            }

            // Ctrl/Cmd + Shift + R: 重置表单
            if ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === 'R') {
                e.preventDefault();
                this.handleResetOrder();
            }
        }

        async handleLogin(e) {
            e.preventDefault();
            
            // **修复**: 从 UIManager 获取唯一的 FormManager 实例
            const formManager = this.uiManager.getManager('form');
            const stateManager = this.uiManager.getManager('state');

            const email = this.elements.emailInput.value.trim();
            const password = this.elements.passwordInput.value.trim();
            const rememberMe = (this.elements.rememberMe && this.elements.rememberMe.checked) || false;

            if (!email || !password) {
                this.uiManager.showAlert('请输入邮箱和密码', 'warning');
                return;
            }

            this.uiManager.setButtonLoading(this.elements.loginBtn, true);

            try {
                const result = await getApiService().login(email, password, rememberMe);
                
                if (result.success) {
                    this.logger.log('登录成功', 'success', { email });
                    this.uiManager.showAlert('登录成功！', 'success');
                } else {
                    this.logger.log('登录失败', 'error', { email, error: result.message });
                    this.uiManager.showAlert(result.message || '登录失败', 'error');
                }
            } catch (error) {
                this.logger.log('登录异常', 'error', { email, error: error.message });
                this.uiManager.showAlert('登录过程中发生错误', 'error');
            } finally {
                this.uiManager.setButtonLoading(this.elements.loginBtn, false);
            }
        }

        handleLogout() {
            this.uiManager.showConfirm(
                '确认登出',
                '您确定要登出吗？',
                () => {
                    try {
                        // 使用统一的登出方法
                        const apiService = getAPIService();
                        if (apiService && typeof apiService.logout === 'function') {
                            apiService.logout();
                        } else {
                            // 备用方案
                            const appState = getAppState();
                            if (appState) {
                                appState.clearAuth();
                            }
                            window.location.reload();
                        }
                    } catch (error) {
                        this.logger.log(`登出失败: ${error.message}`, 'error');
                        // 备用方案
                        const appState = getAppState();
                        if (appState) {
                            appState.clearAuth();
                        }
                        window.location.reload();
                    }
                }
            );
        }


        handleLanguageChange(e) {
            const selectedLanguage = e.target.value;
            this.logger.log(`语言切换请求: ${selectedLanguage}`, 'info');
            
            // **改进**: 使用多种方式获取I18nManager
            const getI18nManager = () => {
                try {
                    // 优先从新的依赖容器获取
                    if (window.OTA && window.OTA.getService) {
                        const manager = window.OTA.getService('i18nManager');
                        if (manager) return manager;
                    }
                    // 后备方案
                    if (window.getI18nManager) {
                        return window.getI18nManager();
                    }
                    // 直接从OTA命名空间获取
                    if (window.OTA && window.OTA.i18nManager) {
                        return window.OTA.i18nManager;
                    }
                    return null;
                } catch (error) {
                    this.logger.log(`获取i18nManager失败: ${error.message}`, 'error');
                    return null;
                }
            };

            const i18nManager = getI18nManager();
            if (!i18nManager) {
                this.logger.log('i18nManager不可用', 'error');
                this.uiManager.showAlert('语言切换功能不可用', 'error', 3000);
                return;
            }

            try {
                i18nManager.setLanguage(selectedLanguage);
                this.logger.log(`语言已切换至: ${selectedLanguage}`, 'success');
                
                // 显示语言切换成功提示
                const languageName = i18nManager.t(`header.language${selectedLanguage.charAt(0).toUpperCase() + selectedLanguage.slice(1)}`);
                this.uiManager.showAlert(`语言已切换至: ${languageName}`, 'success', 2000);
            } catch (error) {
                this.logger.log(`语言切换失败: ${error.message}`, 'error');
                this.uiManager.showAlert(`语言切换失败: ${error.message}`, 'error', 3000);
            }
        }

        handleShowHistory() {
            try {
                // 多种方式获取历史订单管理器
                let historyManager = null;
                
                if (window.getOrderHistoryManager) {
                    try {
                        historyManager = window.getOrderHistoryManager();
                        console.log('✅ 通过 getOrderHistoryManager() 获取管理器成功');
                    } catch (error) {
                        console.error('❌ getOrderHistoryManager() 调用失败:', error);
                    }
                }
                
                if (!historyManager && window.OTA && window.OTA.orderHistoryManager) {
                    historyManager = window.OTA.orderHistoryManager;
                    console.log('✅ 通过 OTA.orderHistoryManager 获取管理器成功');
                }

                if (!historyManager && window.orderHistoryManager) {
                    historyManager = window.orderHistoryManager;
                    console.log('✅ 通过 window.orderHistoryManager 获取管理器成功');
                }

                if (historyManager && historyManager.showHistoryPanel) {
                    console.log('🎯 调用历史订单管理器的 showHistoryPanel...');
                    historyManager.showHistoryPanel();
                } else {
                    console.log('⚠️ 历史订单管理器不可用，使用简化方案...');
                    // 简化后备方案：直接显示面板
                    const historyPanel = document.getElementById('historyPanel');
                    if (historyPanel) {
                        historyPanel.style.display = 'block';
                        historyPanel.classList.remove('hidden');
                        console.log('✅ 历史订单面板已显示（简化方案）');
                    } else {
                        console.error('❌ 历史订单面板元素不存在');
                    }
                    this.logger.log('使用简化方案显示历史订单面板', 'warning');
                }
            } catch (error) {
                console.error('❌ 显示历史订单面板失败:', error);
                this.logger.log(`历史订单显示错误: ${error.message}`, 'error');
            }
        }

        handleReturnToMultiOrder() {
            this.logger.log('开始处理返回多订单模式请求', 'info');

            try {
                // 获取多订单管理器
                const multiOrderManager = (window.OTA && window.OTA.multiOrderManager) ||
                                        window.multiOrderManager ||
                                        (window.getMultiOrderManager && window.getMultiOrderManager());

                if (!multiOrderManager) {
                    throw new Error('无法获取MultiOrderManager实例');
                }

                // 检查是否有已解析的订单数据
                const parsedOrders = multiOrderManager.state && multiOrderManager.state.parsedOrders;
                if (!parsedOrders || parsedOrders.length === 0) {
                    this.logger.log('没有找到已解析的多订单数据', 'warning');
                    this.uiManager.showAlert('没有可返回的多订单数据，请重新解析订单', 'warning');
                    return;
                }

                // 重新显示多订单面板
                multiOrderManager.showMultiOrderPanel(parsedOrders);
                
                // 隐藏返回按钮
                const returnBtn = document.getElementById('returnToMultiOrder');
                if (returnBtn) {
                    returnBtn.classList.add('hidden');
                }

                this.logger.log(`成功返回多订单模式，显示${parsedOrders.length}个订单`, 'success');

            } catch (error) {
                this.logger.log(`返回多订单模式失败: ${error.message}`, 'error');
                this.uiManager.showAlert('返回多订单模式失败，请重新解析订单', 'error');
            }
        }

        async handleParseOrder() {
            const orderText = this.elements.orderInput.value.trim();
            
            if (!orderText) {
                this.uiManager.showAlert('请输入订单描述', 'warning');
                return;
            }

            if (!getGeminiService().isAvailable()) {
                this.uiManager.showApiKeyPrompt();
                return;
            }

            this.uiManager.setButtonLoading(this.elements.parseBtn, true);

            try {
                // 🚀 与自动解析使用相同的parseOrder方法
                const parseResult = await getGeminiService().parseOrder(orderText);
                
                if (!parseResult || !Array.isArray(parseResult) || parseResult.length === 0) {
                    throw new Error('解析失败或无有效订单数据');
                }

                this.logger.log(`✅ 手动解析完成，检测到 ${parseResult.length} 个订单`, 'success');

                // 🎯 与自动解析使用相同的判断逻辑
                if (parseResult.length > 1) {
                    this.uiManager.showAlert(`检测到 ${parseResult.length} 个订单，显示多订单面板`, 'success');
                    const multiOrderResult = {
                        isMultiOrder: true,
                        orderCount: parseResult.length,
                        orders: parseResult,
                        confidence: this.calculateAverageConfidence(parseResult),
                        analysis: `手动解析检测到${parseResult.length}个订单`
                    };
                    
                    // 触发多订单事件（与自动解析完全相同）
                    const event = new CustomEvent('multiOrderDetected', {
                        detail: {
                            multiOrderResult: multiOrderResult,
                            orderText: orderText
                        }
                    });
                    document.dispatchEvent(event);
                    
                    this.logger.log('🎉 手动解析：多订单事件已触发', 'success');
                    
                } else {
                    const orderData = parseResult[0];
                    
                    getAppState().setCurrentOrder({
                        rawText: orderText,
                        parsedData: orderData,
                        confidence: this.calculateDataConfidence(orderData),
                        timestamp: Date.now(),
                        source: 'manual-parse'
                    });

                    this.logger.log('✅ 手动解析：单订单处理完成', 'success', { orderData });
                    this.uiManager.showAlert('订单解析成功！', 'success');
                }
                
            } catch (error) {
                this.logger.logError('手动订单解析失败', error);
                this.uiManager.showAlert('订单解析过程中发生错误', 'error');
            } finally {
                this.uiManager.setButtonLoading(this.elements.parseBtn, false);
            }
        }

        calculateAverageConfidence(orders) {
            if (!orders || orders.length === 0) return 0;
            
            const confidences = orders.map(order => this.calculateDataConfidence(order));
            return Math.round(confidences.reduce((sum, conf) => sum + conf, 0) / confidences.length);
        }

        async handleCreateOrder(e) {
            if (e) e.preventDefault();

            // **修复**: 从UIManager获取FormManager
            const formManager = this.uiManager.getManager('form');
            if (!formManager) return;

            const orderData = formManager.collectFormData();

            // 先进行字段级验证
            const fieldIssues = this.detectFieldIssues(orderData);

            // 清除所有现有错误
            this.clearAllFieldErrors(formManager);

            if (Object.keys(fieldIssues).length > 0) {
                // 显示字段级错误（非阻断）
                Object.entries(fieldIssues).forEach(([fieldName, message]) => {
                    formManager.showFieldError(fieldName, message);
                });

                const errorCount = Object.keys(fieldIssues).length;
                // 仅提示，不阻断创建流程（改为轻量Toast，不再使用顶部警报）
                this.logger.log(`发现 ${errorCount} 个字段问题（非阻断继续）`, 'warning', { fieldIssues });
                this.uiManager.showQuickToast(`⚠️ 发现 ${errorCount} 个字段问题，但将继续创建订单`, 'warning');
                // 注意：不再 return，继续执行创建逻辑
            }

            // **新增**: 强制确保负责人字段存在
            if (!orderData.incharge_by_backend_user_id) {
                const defaultBackendUserId = getApiService().getDefaultBackendUserId();
                if (defaultBackendUserId) {
                    orderData.incharge_by_backend_user_id = defaultBackendUserId;
                    this.logger.log(`handleCreateOrder: 补充设置负责人ID: ${defaultBackendUserId}`, 'info');
                } else {
                    // 如果还是无法获取，使用紧急默认值
                    orderData.incharge_by_backend_user_id = 1;
                    this.logger.log('handleCreateOrder: 使用紧急默认负责人ID: 1', 'warning');
                }
            }

            // 验证数据（非阻断模式）
            const validation = getApiService().validateOrderData(orderData);
            if (validation.errors && validation.errors.length > 0) {
                formManager.showValidationErrors(validation.errors);
                this.logger.log('订单验证发现问题（非阻断）', 'warning', { errors: validation.errors, orderData });
                // 不return，继续执行API调用
            }

            if (validation.warnings.length > 0) {
                this.uiManager.showAlert(`警告: ${validation.warnings.join(', ')}`, 'warning');
            }

            this.uiManager.setButtonLoading(this.elements.createBtn, true);

            try {
                const result = await getApiService().createOrder(orderData);
                
                if (result.success) {
                    // 提取订单ID
                    const orderId = (result.data && result.data.id) ||
                                  (result.data && result.data.order_id) ||
                                  result.id || 'N/A';
                    
                    this.logger.log('订单创建成功', 'success', { orderId, orderData, response: result });

                    // **新增**: 记录成功的订单到历史
                    try {
                        const historyManager = this.getOrderHistoryManager();
                        if (historyManager) {
                            historyManager.addOrder(orderData, orderId, result);
                            this.logger.log('订单已记录到历史', 'info', { orderId });
                        }
                    } catch (historyError) {
                        this.logger.logError('记录订单历史失败', historyError);
                    }

                    // 显示简洁的成功提示
                    this.uiManager.showSimpleSuccessToast(orderId);

                    // 自动重置表单（无确认对话框）
                    formManager.resetForm();
                    
                    // 重新填充选项（确保下拉框数据完整）
                    setTimeout(() => {
                        formManager.populateFormOptions();
                    }, 100);

                } else {
                    const errorMessage = result.message || result.error || '订单创建失败';
                    getLogger().log('订单创建失败', 'error', {
                        error: errorMessage,
                        orderData,
                        response: result
                    });

                    // **新增**: 记录失败的订单到历史
                    try {
                        const historyManager = this.getOrderHistoryManager();
                        if (historyManager) {
                            // 记录失败的订单，使用特殊的订单ID标识
                            const failedOrderId = `FAILED_${Date.now()}`;
                            historyManager.addOrder(orderData, failedOrderId, {
                                success: false,
                                message: errorMessage,
                                error: result,
                                timestamp: new Date().toISOString()
                            });
                            getLogger().log('失败订单已记录到历史', 'info', { failedOrderId });
                        }
                    } catch (historyError) {
                        getLogger().logError('记录失败订单历史失败', historyError);
                    }

                    // 创建错误对象并显示详细错误弹窗
                    const errorObj = new Error(errorMessage);
                    errorObj.details = result;
                    errorObj.requestData = orderData;

                    // 如果API返回了验证错误，添加到错误对象中
                    if (result.data && result.data.validation_error) {
                        errorObj.validationErrors = result.data.validation_error;
                    }

                    this.uiManager.showErrorModal(errorObj, {
                        title: 'Order Creation Failed'
                    });
                }
            } catch (error) {
                getLogger().log('订单创建异常', 'error', {
                    error: error.message,
                    originalError: error.originalError && error.originalError.message,
                    errorDetails: error.details,
                    orderData
                });

                // **新增**: 记录异常的订单到历史
                try {
                    const historyManager = this.getOrderHistoryManager();
                    if (historyManager) {
                        const exceptionOrderId = `EXCEPTION_${Date.now()}`;
                        historyManager.addOrder(orderData, exceptionOrderId, {
                            success: false,
                            message: `异常: ${error.message}`,
                            error: {
                                name: error.name,
                                message: error.message,
                                stack: error.stack
                            },
                            timestamp: new Date().toISOString()
                        });
                        getLogger().log('异常订单已记录到历史', 'info', { exceptionOrderId });
                    }
                } catch (historyError) {
                    getLogger().logError('记录异常订单历史失败', historyError);
                }

                // 确保错误弹窗总是显示
                try {
                    // 检查是否是重复订单错误
                    if (error.isDuplicateOrder) {
                        this.uiManager.showErrorModal(error, {
                            title: '⚠️ Duplicate Order Detected'
                        });
                    } else {
                        this.uiManager.showErrorModal(error, {
                            title: 'Order Creation Failed'
                        });
                    }
                } catch (uiError) {
                    getLogger().logError('显示错误弹窗失败', uiError);
                    // 如果弹窗显示失败，至少显示简单的alert
                    alert(`Order Creation Failed: ${error.message}`);
                }
            } finally {
                this.uiManager.setButtonLoading(this.elements.createBtn, false);
            }
        }

        /**
         * 处理订单验证
         */
        handleValidateOrder() {
            // **修复**: 从UIManager获取FormManager
            const formManager = this.uiManager.getManager('form');
            if (!formManager) return;

            const orderData = formManager.collectFormData();
            const validation = getApiService().validateOrderData(orderData);

            if (validation.isValid) {
                this.uiManager.showAlert('数据验证通过！', 'success');
            } else {
                formManager.showValidationErrors(validation.errors);
            }

            if (validation.warnings.length > 0) {
                this.uiManager.showAlert(`警告: ${validation.warnings.join(', ')}`, 'warning');
            }
        }

        /**
         * 处理数据异常提示 - 改为字段级错误显示
         */
        handleValidateData() {
            try {
                // **修复**: 从UIManager获取FormManager
                const formManager = this.uiManager.getManager('form');
                if (!formManager) return;

                const data = formManager.collectFormData();
                const fieldIssues = this.detectFieldIssues(data);

                // 清除所有现有错误
                this.clearAllFieldErrors(formManager);

                if (Object.keys(fieldIssues).length === 0) {
                    this.uiManager.showQuickToast('✅ 数据检查正常', 'success');
                } else {
                    // 显示字段级错误
                    Object.entries(fieldIssues).forEach(([fieldName, message]) => {
                        formManager.showFieldError(fieldName, message);
                    });

                    const errorCount = Object.keys(fieldIssues).length;
                    this.uiManager.showQuickToast(`⚠️ 发现 ${errorCount} 个字段问题，请检查红色边框字段`, 'warning');
                }
            } catch (error) {
                getLogger().log('数据验证失败', 'error', { error: error.message });
                this.uiManager.showQuickToast('❌ 数据验证失败', 'error');
            }
        }

        /**
         * 检测字段级数据异常
         * @param {Object} data - 订单数据
         * @returns {Object} 字段问题映射 {fieldName: errorMessage}
         */
        detectFieldIssues(data) {
            const fieldIssues = {};

            // 获取国际化管理器
            const i18nManager = window.OTA.getService('i18nManager');

            // 基本信息检查 - 使用正确的API字段名
            if (!data.customer_name) {
                fieldIssues.customerName = i18nManager.t('validation.customerNameRequired');
            }

            if (!data.customer_contact && !data.customer_email) {
                if (!data.customer_contact) {
                    fieldIssues.customerContact = i18nManager.t('validation.customerContactRequired');
                }
                if (!data.customer_email) {
                    fieldIssues.customerEmail = i18nManager.t('validation.customerEmailRequired');
                }
            }

            // **修复**: 使用正确的API字段名进行验证
            if (!data.pickup_location) {
                fieldIssues.pickup = i18nManager.t('validation.pickupRequired');
            }

            if (!data.dropoff_location) {
                fieldIssues.dropoff = i18nManager.t('validation.dropoffRequired');
            }

            if (!data.pickup_date) {
                fieldIssues.pickupDate = i18nManager.t('validation.serviceDateRequired');
            }

            if (!data.pickup_time) {
                fieldIssues.pickupTime = i18nManager.t('validation.serviceTimeRequired');
            }

            if (!data.ota_reference_number) {
                fieldIssues.otaReferenceNumber = i18nManager.t('validation.otaReferenceRequired');
            }

            // 数值范围检查 - 使用正确的API字段名
            if (data.passenger_number && data.passenger_number > 20) {
                fieldIssues.passengerCount = i18nManager.t('validation.passengerCountInvalid');
            }

            if (data.luggage_count && data.luggage_count > 50) {
                fieldIssues.luggageCount = i18nManager.t('validation.luggageCountInvalid');
            }

            // 邮箱格式检查
            if (data.customer_email && !this.isValidEmail(data.customer_email)) {
                fieldIssues.customerEmail = i18nManager.t('validation.emailFormatInvalid');
            }

            // 电话格式检查
            if (data.customer_contact && !this.isValidPhone(data.customer_contact)) {
                fieldIssues.customerContact = i18nManager.t('validation.phoneFormatInvalid');
            }

            return fieldIssues;
        }

        /**
         * 清除所有字段错误
         * @param {Object} formManager - 表单管理器实例
         */
        clearAllFieldErrors(formManager) {
            const fieldNames = [
                'customerName', 'customerContact', 'customerEmail', 'pickup', 'dropoff',
                'pickupDate', 'pickupTime', 'otaReferenceNumber', 'passengerCount',
                'luggageCount', 'flightInfo', 'extraRequirement'
            ];

            fieldNames.forEach(fieldName => {
                formManager.clearFieldError(fieldName);
            });
        }

        /**
         * 验证邮箱格式
         * @param {string} email - 邮箱地址
         * @returns {boolean} 是否有效
         */
        isValidEmail(email) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailRegex.test(email);
        }

        /**
         * 验证电话格式
         * @param {string} phone - 电话号码
         * @returns {boolean} 是否有效
         */
        isValidPhone(phone) {
            const phoneRegex = /^[\d+\-\s()]+$/;
            return phoneRegex.test(phone) && phone.replace(/\D/g, '').length >= 8;
        }

        /**
         * 检测数据异常 - 保留原方法以兼容其他调用
         * @param {Object} data - 订单数据
         * @returns {Array} 问题列表
         */
        detectDataIssues(data) {
            const fieldIssues = this.detectFieldIssues(data);
            const issues = Object.values(fieldIssues);

            // 日期有效性检查
            if (data.pickup_date) {
                const pickupDate = new Date(data.pickup_date);
                const today = new Date();
                today.setHours(0, 0, 0, 0);

                if (pickupDate < today) {
                    issues.push('接送日期已过期');
                }

                const oneYearLater = new Date();
                oneYearLater.setFullYear(oneYearLater.getFullYear() + 1);

                if (pickupDate > oneYearLater) {
                    issues.push('接送日期过于遥远（超过一年）');
                }
            }

            // 价格检查
            if (data.ota_price && data.ota_price < 0) {
                issues.push('价格不能为负数');
            }

            if (data.ota_price && data.ota_price > 10000) {
                issues.push('价格过高（超过10000）');
            }

            return issues;
        }

        /**
         * 处理订单重置
         */
        handleResetOrder() {
            // **修复**: 从UIManager获取FormManager
            const formManager = this.uiManager.getManager('form');
            if (!formManager) return;

            this.uiManager.showConfirm(
                '重置表单',
                '您确定要重置表单吗？这将清除所有已填写的内容。',
                () => {
                    formManager.resetForm();
                }
            );
        }

        /**
         * 处理主题切换
         */
        handleThemeToggle() {
            const currentTheme = getAppState().get('config.theme');
            const newTheme = currentTheme === 'light' ? 'dark' : 'light';
            
            // 保存到应用状态
            getAppState().set('config.theme', newTheme);
            
            // 持久化保存到localStorage
            localStorage.setItem('ota_theme_preference', newTheme);
            
            getLogger().log(`主题已切换至: ${newTheme}`, 'info');
            
            // 显示切换成功提示，自动关闭
            const i18nManager = window.getI18nManager();
            const themeText = newTheme === 'light' ? '亮色' : '暗色';
            const message = i18nManager ? 
                i18nManager.t('messages.themeChanged', { theme: themeText }) : 
                `主题已切换至${themeText}模式`;
            this.uiManager.showAlert(message, 'success', 2000);
        }

        /**
         * 处理清空日志
         */
        handleClearLogs() {
            getLogger().clear();
        }

        /**
         * 处理导出日志
         */
        handleExportLogs() {
            const logs = getLogger().export();
            const blob = new Blob([JSON.stringify(logs, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);

            const a = document.createElement('a');
            a.href = url;
            a.download = `ota-logs-${new Date().toISOString().slice(0, 10)}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);

            this.uiManager.showAlert('日志已导出', 'success');
        }

        /**
         * 处理调试模式变化
         */
        handleDebugModeChange() {
            const enabled = this.elements.debugMode.checked;
            getAppState().setDebugMode(enabled);
            getLogger().log(`调试模式${enabled ? '已启用' : '已禁用'}`, 'info');
        }

        /**
         * 处理字段编辑
         * @param {HTMLElement} button - 编辑按钮
         */
        handleFieldEdit(button) {
            const fieldName = button.getAttribute('data-field');
            const fieldGroup = button.closest('.editable-field');
            const field = fieldGroup.querySelector('input, textarea, select');

            if (!field) return;

            if (fieldGroup.classList.contains('editing')) {
                // 保存编辑
                this.saveFieldEdit(fieldGroup, field);
            } else {
                // 开始编辑
                this.startFieldEdit(fieldGroup, field);
            }
        }

        /**
         * 开始字段编辑
         * @param {HTMLElement} fieldGroup - 字段组容器
         * @param {HTMLElement} field - 字段元素
         */
        startFieldEdit(fieldGroup, field) {
            fieldGroup.classList.add('editing');
            field.focus();

            // 如果是输入框，选中所有文本
            if (field.tagName === 'INPUT' || field.tagName === 'TEXTAREA') {
                field.select();
            }
        }

        /**
         * 保存字段编辑
         * @param {HTMLElement} fieldGroup - 字段组容器
         * @param {HTMLElement} field - 字段元素
         */
        saveFieldEdit(fieldGroup, field) {
            fieldGroup.classList.remove('editing');

            // 触发change事件以更新应用状态
            const event = new Event('change', { bubbles: true });
            field.dispatchEvent(event);

            getLogger().log('字段编辑已保存', 'info', {
                fieldName: field.name || field.id,
                value: field.value
            });
        }

        /**
         * 处理清空输入
         */
        handleClearInput() {
            this.uiManager.showConfirm(
                '清空输入',
                '您确定要清空当前输入的内容吗？',
                () => {
                    this.performComprehensiveClear();
                }
            );
        }

        /**
         * 执行全面清理操作
         * 清理所有相关数据但保护关键信息
         */
        performComprehensiveClear() {
            const logger = getLogger();
            logger.log('🧹 开始执行全面清理操作...', 'info');

            try {
                // 1. 清理表单输入字段
                if (this.elements.orderInput) {
                    this.elements.orderInput.value = '';
                }

                // 2. 清理表单管理器状态
                const formManager = this.uiManager.getManager('form');
                if (formManager && typeof formManager.resetForm === 'function') {
                    formManager.resetForm();
                }

                // 3. 清理实时分析状态
                const realtimeManager = this.uiManager.getManager('realtimeAnalysis');
                if (realtimeManager && typeof realtimeManager.clearRealtimeAnalysis === 'function') {
                    realtimeManager.clearRealtimeAnalysis();
                }

                // 4. 清理多订单管理器状态
                if (window.OTA && window.OTA.multiOrderManager) {
                    window.OTA.multiOrderManager.hideMultiOrderPanel();
                }

                // 5. 清理当前订单状态（但保护历史记录）
                const appState = getAppState();
                if (appState) {
                    appState.set('currentOrder', null);
                    appState.set('currentAnalysis', null);
                }

                // 6. 清理UI显示状态
                this.clearUIDisplayState();

                // 7. 重置Gemini状态显示
                this.resetGeminiStatusDisplay();

                logger.log('✅ 全面清理操作完成', 'success');

            } catch (error) {
                logger.logError('全面清理操作失败', error);
            }
        }

        /**
         * 清理UI显示状态
         */
        clearUIDisplayState() {
            try {
                // 清理进度指示器
                const progressIndicators = document.querySelectorAll('.progress-indicator, .loading-spinner');
                progressIndicators.forEach(indicator => {
                    indicator.style.display = 'none';
                });

                // 清理错误提示
                const errorMessages = document.querySelectorAll('.error-message, .validation-message');
                errorMessages.forEach(message => {
                    message.remove();
                });

                // 清理成功提示
                const successMessages = document.querySelectorAll('.success-message, .field-success-message');
                successMessages.forEach(message => {
                    message.remove();
                });

                getLogger().log('UI显示状态已清理', 'info');
            } catch (error) {
                getLogger().logError('清理UI显示状态失败', error);
            }
        }

        /**
         * 重置Gemini状态显示
         */
        resetGeminiStatusDisplay() {
            try {
                const geminiStatus = document.getElementById('geminiStatus');
                if (geminiStatus) {
                    geminiStatus.textContent = '请输入订单描述';
                    geminiStatus.className = 'gemini-status';
                }

                const geminiIndicator = document.querySelector('.gemini-indicator');
                if (geminiIndicator) {
                    geminiIndicator.style.display = 'none';
                }

                getLogger().log('Gemini状态显示已重置', 'info');
            } catch (error) {
                getLogger().logError('重置Gemini状态显示失败', error);
            }
        }

        /**
         * 处理手动多订单检测
         */
        handleManualMultiOrderDetection() {
            const logger = getLogger();

            if (!this.elements.orderInput || !this.elements.orderInput.value.trim()) {
                this.uiManager.showAlert('提示', '请先输入订单内容再进行多订单检测。');
                return;
            }

            const orderText = this.elements.orderInput.value.trim();

            // 检查最小长度要求
            if (orderText.length < 50) {
                this.uiManager.showAlert('提示', '订单内容过短，建议至少输入50个字符以获得更好的检测效果。');
                return;
            }

            // 显示确认对话框（如果启用）
            const multiOrderManager = window.OTA && window.OTA.multiOrderManager;
            if (!multiOrderManager) {
                this.uiManager.showAlert('错误', '多订单管理器不可用，请刷新页面重试。');
                return;
            }

            const config = multiOrderManager.config && multiOrderManager.config.manualDetection;
            if (config && config.confirmBeforeDetection) {
                this.uiManager.showConfirm(
                    '手动多订单检测',
                    '确定要对当前输入内容进行多订单检测吗？这将使用AI分析文本并可能显示多订单面板。',
                    () => {
                        this.performManualMultiOrderDetection(orderText);
                    }
                );
            } else {
                this.performManualMultiOrderDetection(orderText);
            }
        }

        /**
         * 执行手动多订单检测
         * @param {string} orderText - 订单文本
         */
        async performManualMultiOrderDetection(orderText) {
            const logger = getLogger();

            try {
                logger.log('🔍 开始手动多订单检测...', 'info');

                // 显示加载状态
                const manualBtn = document.getElementById('manualMultiOrderDetection');
                if (manualBtn) {
                    manualBtn.disabled = true;
                    manualBtn.innerHTML = '🔄 检测中...';
                }

                // 调用多订单管理器进行检测
                const multiOrderManager = window.OTA && window.OTA.multiOrderManager;
                if (multiOrderManager) {
                    // 强制触发多订单检测，忽略自动触发条件
                    await multiOrderManager.analyzeInputForMultiOrder(orderText, {
                        forceDetection: true,
                        source: 'manual'
                    });

                    logger.log('✅ 手动多订单检测完成', 'success');
                } else {
                    throw new Error('多订单管理器不可用');
                }

            } catch (error) {
                logger.logError('手动多订单检测失败', error);
                this.uiManager.showAlert('检测失败', `多订单检测过程中发生错误：${error.message}`);
            } finally {
                // 恢复按钮状态
                const manualBtn = document.getElementById('manualMultiOrderDetection');
                if (manualBtn) {
                    manualBtn.disabled = false;
                    manualBtn.innerHTML = '🔍 多订单检测';
                }
            }
        }

        /**
         * 处理清除保存账号
         */
        handleClearSaved() {
            // **修复**: 从UIManager获取FormManager
            const formManager = this.uiManager.getManager('form');
            if (!formManager) return;

            this.uiManager.showConfirm(
                '清除保存的账号',
                '您确定要清除保存的账号信息吗？',
                () => {
                    formManager.clearSavedAccountInfo();
                }
            );
        }

        /**
         * 显示模态框
         * @param {string} title - 标题
         * @param {string} content - 内容
         * @param {Object} options - 选项
         */
        showModal(title, content, options = {}) {
            this.currentModal = this.elements.modal;
            this.uiManager.showModal(title, content, options);
        }

        /**
         * 隐藏模态框
         */
        hideModal() {
            this.currentModal = null;
            this.uiManager.hideModal();
        }

        /**
         * 显示预览模态框
         */
        showPreviewModal() {
            this.uiManager.showPreviewModal();
        }

        /**
         * 隐藏预览模态框
         */
        hidePreviewModal() {
            this.uiManager.hidePreviewModal();
        }

        /**
         * 计算数据置信度
         * @param {object} orderData - 订单数据
         * @returns {number} 置信度百分比
         */
        calculateDataConfidence(orderData) {
            if (!orderData || typeof orderData !== 'object') {
                return 0;
            }

            // 重要字段权重
            const importantFields = {
                'customer_name': 15,
                'customer_contact': 10,
                'pickup_location': 20,
                'dropoff_location': 20,
                'pickup_date': 15,
                'pickup_time': 10,
                'ota_price': 5,
                'ota_reference_number': 5
            };

            let filledWeight = 0;
            let totalWeight = 0;

            for (const [field, weight] of Object.entries(importantFields)) {
                totalWeight += weight;
                const value = orderData[field];
                if (value !== null && value !== undefined && value !== '' && value !== 0) {
                    filledWeight += weight;
                }
            }

            return Math.round((filledWeight / totalWeight) * 100);
        }

        /**
         * 防抖工具函数
         * @param {Function} func - 要防抖的函数
         * @param {number} delay - 延迟时间（毫秒）
         * @returns {Function} 防抖后的函数
         */
        debounce(func, delay) {
            const key = func.name || 'anonymous';
            
            return (...args) => {
                // 清除之前的计时器
                if (this.debounceTimers.has(key)) {
                    clearTimeout(this.debounceTimers.get(key));
                }
                
                // 设置新的计时器
                const timer = setTimeout(() => {
                    func.apply(this, args);
                    this.debounceTimers.delete(key);
                }, delay);
                
                this.debounceTimers.set(key, timer);
            };
        }

        /**
         * 安全地获取OrderHistoryManager实例
         * @returns {OrderHistoryManager|null} 历史管理器实例或null
         */
        getOrderHistoryManager() {
            try {
                // 尝试多种方式获取OrderHistoryManager
                if (window.getOrderHistoryManager) {
                    return window.getOrderHistoryManager();
                } else if (window.OTA && window.OTA.orderHistoryManager) {
                    return window.OTA.orderHistoryManager;
                } else if (window.OrderHistoryManager) {
                    return new window.OrderHistoryManager();
                }
                
                getLogger().log('无法获取OrderHistoryManager实例', 'warning');
                return null;
            } catch (error) {
                getLogger().logError('获取OrderHistoryManager失败', error);
                return null;
            }
        }

    }

    // 导出到全局命名空间
    window.OTA.managers.EventManager = EventManager;

})();
