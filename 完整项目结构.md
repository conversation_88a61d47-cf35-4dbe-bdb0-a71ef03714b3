# OTA订单处理系统 - 完整项目结构

## 技术栈与运行环境

- 前端：原生 HTML/CSS/JavaScript（无打包器），脚本以 script 标签按严格顺序加载。
- 运行模式：静态 SPA（可直接打开 index.html；推荐简单静态服务器）。
- 部署：Netlify（publish="."，SPA redirects）。Node 18 用于函数与验证脚本。
- 外部服务：
  - GoMyHire API（https://gomyhire.com.my/api）用于订单创建与系统静态数据。
  - Google Generative Language API（Gemini v1beta）用于文本/图片解析与多订单检测。
  - Netlify Functions: /.netlify/functions/flight-info（航班查询，生产/本地切换）。
- CSP/connect-src（见 netlify.toml）允许：gomyhire.com.my、generativelanguage.googleapis.com、aeroapi.flightaware.com。

## 架构分层与模块映射

- 核心基础（Core）
  - 依赖注入与服务定位：js/core/dependency-container.js、js/core/service-locator.js
  - 启动引导：js/core/application-bootstrap.js
  - 应用状态与日志：js/app-state.js、js/logger.js
  - 国际化：js/i18n.js
  - 工具：js/utils.js
- 服务（Services）
  - ApiService：js/api-service.js（鉴权、系统数据、订单创建、权限与预处理）
  - GeminiService：js/gemini-service.js（文本/图片解析、多订单检测、KB/翻译、恢复策略）
  - FlightInfoService：js/flight-info-service.js（Netlify 函数代理）
  - CurrencyConverter、ImageUploadManager 等：js/currency-converter.js、js/image-upload-manager.js
- 业务管理器（Managers）
  - UIManager：js/ui-manager.js（聚合 UI 与子管理器）
  - MultiOrderManagerV2：js/multi-order-manager-v2.js（多订单 UI/批量创建编排）
  - LanguageManager：js/language-manager.js（语言目录/校验/转换/缓存）
  - PagingServiceManager：js/paging-service-manager.js（举牌服务检测与派生单）
  - OrderHistoryManager：js/order-history-manager.js（本地历史记录与导出/复制）
  - MonitoringWrapper：js/monitoring-wrapper.js（工厂/异步方法监控包装）
- 多订单子系统（js/multi-order/*）
  - Coordinator：multi-order-coordinator.js（汇聚 detector/processor/renderer/state/batch）
  - Detector：multi-order-detector.js（Gemini优先+传统启发式多订单检测/分割）
  - Processor：multi-order-processor.js（逐单解析重试、应用映射/校验、创建API调用）
  - Renderer：multi-order-renderer.js（浮窗面板、卡片、拖拽、选择、统计）
  - StateManager：state-manager.js（会话/选择/批处理/统计，localStorage 持久化）
  - Transformer：multi-order-transformer.js（备用字段→前端→API、特殊规则、默认值、一致性）
  - BatchProcessor：batch-processor.js（并发/超时/重试的批量创建）
  - FieldMappingConfig/Validator：映射表/非阻断式校验/对象比较报告
  - SystemIntegrityChecker：system-integrity-checker.js（装配健康巡检）
- OTA 集成与策略
  - ota-system/*、ota-channel-mapping.js（渠道映射/策略，含 Fliggy 定制）
  - adapters/、strategies/、shadow/ 补丁与策略文件（根据页面测试 HTML 触发）

## 初始化顺序与全局暴露

- index.html 脚本加载顺序（关键）：Core → utils/services/managers → UI manager → main.js
- Bootstrap 流程（application-bootstrap.js）：
  1) dependencies（容器/定位器注册）
  2) services（Api/Gemini/Logger/AppState 等）
  3) managers（UI、MultiOrder、Language 等）
  4) ui（i18n 初始化后渲染 UI）
  5) finalization（健康检查/调试面板）
- 全局暴露：window.OTA.{container, serviceLocator, apiService, geminiService, ...} 与 getXxx 工厂函数；debug 面板：window.OTA.debug。

## 依赖关系网（精细化）

- 服务级依赖
  - ApiService ← AppState（鉴权状态）/Logger/LanguageManager（语言格式）/Utils（请求封装）
  - GeminiService ← fetch（Generative API）/AppState（系统映射与配置）/Logger/Utils/LanguageManager（语言/地址翻译）
  - FlightInfoService ← Netlify Functions（后端代理）
  - LanguageManager ← AppState（systemData.languages）/Logger；被 ApiService/Transformer/Validator/UI 使用
  - PagingServiceManager ← Logger；被 Transformer/多订单流程使用
  - OrderHistoryManager ← AppState/Logger/Clipboard API/UIManager（toast）/I18n；被 Processor 创建成功后调用
  - MonitoringWrapper ← Logger；包装 getAppState/getGeminiService/getAPIService 等工厂与已存在实例方法
- 多订单子系统依赖
  - Coordinator → Detector, Processor, Renderer, BatchProcessor, StateManager（通过 Registry 或 window.OTA 回退）
  - Detector → GeminiService（detectAndSplitMultiOrdersWithVerification）、FieldMappingConfig（AI→前端映射）
  - Processor → GeminiService（parseOrder）、ApiService（preprocess/validate/create）、Transformer、OrderHistoryManager
  - Renderer → ApiService.staticData（车类型名）、UI DOM、MultiOrderManager（字段快速编辑桥）
  - StateManager → localStorage（定时保存）、自有 listeners
  - Transformer → FieldMappingConfig/Validator、PagingServiceManager、LanguageManager（建议统一其 API 格式化）
  - BatchProcessor → ApiService（createOrder）、Semaphore（并发）、CustomizationEngine（按渠道定制可选）
- 事件网
  - Coordinator 分发：multiOrderModeEnabled/Disabled、multiOrderDetected、orderSelectionChanged、batchProgressUpdated、batchProcessingComplete
  - 监听：multiOrderModeChanged（系统桥接事件），Renderer 监听复选框变更更新 UI 标签
- 外部 API 网
  - generativelanguage.googleapis.com（Gemini 文本/图像）
  - gomyhire.com.my/api（订单/系统数据）
  - Netlify function /flight-info（航班信息）

提示：此依赖可视化为（节点 → 依赖）：
- Coordinator → Detector, Processor, Renderer, BatchProcessor, StateManager, ServiceRegistry
- Detector → GeminiService, FieldMappingConfig
- Processor → GeminiService, ApiService, Transformer, OrderHistoryManager
- Transformer → FieldMappingConfig, FieldMappingValidator, PagingServiceManager, LanguageManager
- Renderer → ApiService.staticData, DOM
- StateManager → localStorage
- ApiService → AppState, Logger, LanguageManager
- GeminiService → Generative API, AppState, Logger
- LanguageManager → AppState, Logger
- OrderHistoryManager → AppState, Logger, UIManager/I18n/Clipboard
- MonitoringWrapper → Logger, Global factories

## 数据流与状态/缓存/持久化

- 单订单主路径
  - 原始文本/图片 → GeminiService.parseOrder/analyzeImage → Transformer（备用→前端→API、特殊格式修复）→ ApiService.preprocess/validate → ApiService.createOrder → OrderHistoryManager.addOrder
- 多订单主路径
  - 文本 → Detector.detectMultiOrder（Gemini 优先+启发式）→ Coordinator.enableMultiOrderMode → Processor.processOrders（解析/转换/校验）→ Renderer.showMultiOrderPanel（卡片/选中/统计）→ Processor.createSelectedOrders（逐单调用 ApiService）→ OrderHistory 保存
- 举牌派生流
  - PagingServiceManager.detect + Transformer.processPagingServiceForOrder → 标记 needs_paging_service / 生成举牌单
- 状态与缓存
  - AppState：鉴权/系统数据/配置（持久化+TTL）
  - MultiOrderStateManager：会话/选择/批处理/统计（localStorage 定时保存）
  - LanguageManager：主缓存/启用子集缓存/验证缓存/API转换缓存（LRU 清理与后台刷新）
  - OrderHistoryManager：按用户邮箱分桶存储历史（localStorage）

## 通用抽象与关键约束

- DI/Service Locator：首选通过 Registry/getService 获取实例；多数模块保留 window.OTA 回退（技术债但可用）。
- 字段转换/校验：
  - FieldMappingConfig：AI_TO_FRONTEND、FRONTEND_TO_API、ALTERNATIVE_FIELDS、REQUIRED_*、SPECIAL_FIELD_RULES（languagesIdArray→对象）
  - FieldMappingValidator：非阻断式必填/类型/格式校验 + 映射完整性比较
  - MultiOrderTransformer：串联备用映射→AI→前端→特殊规则→默认值→一致性→API 格式
- 知识库与解析恢复（GeminiService）：
  - hotel/airport 数据、prompt 策略、JSON 抽取/修复、多次重试与降级解析
- 监控包装（MonitoringWrapper）：
  - 工厂与异步方法包装，performance.mark/measure 与日志记录

## 快速定位指引（开发/优化/修正）

- 我需要改“某字段映射/格式”
  - 先看 field-mapping-config.js（AI_TO_FRONTEND/FRONTEND_TO_API/SPECIAL_FIELD_RULES）。
  - 若是语言数组，建议统一委托 `LanguageManager.transformForAPISync()`，并删去分散的重复转换。
  - 涉及预处理/校验：`api-service.js`（preprocessOrderData/validateOrderData）、field-mapping-validator.js。
- 我需要“新增/调整 多订单卡片 UI”
  - 改 multi-order-renderer.js（generateOrderCardHTML/generateOrderSummary），注意 carType 名查询依赖 ApiService.staticData。
- 我需要“排查多订单检测/分割”
  - 看 multi-order-detector.js（detectMultiOrderAI/smartSplitOrderText 与传统规则）与 `gemini-service.js`（detectAndSplitMultiOrdersWithVerification）。
- 我需要“批量创建并发/节流”
  - 看 batch-processor.js（Semaphore、requestDelay/retry）、multi-order-processor.js（config.maxRetries/batchDelay）。
- 我需要“举牌服务联动”
  - 检查 paging-service-manager.js 与 multi-order-transformer.js 的 detect/processPagingServiceForOrder。
- 我需要“语言联动/默认/校验/缓存”
  - 统一用 language-manager.js（getLanguagesSync/getDefaultSelectionSync/validateLanguageIdsSync/transformForAPISync）。
- 我需要“航班信息接入”
  - flight-info-service.js 调 Netlify 函数；确保函数已部署，开发本地基址正确。
- 我需要“历史记录/导出/复制”
  - order-history-manager.js：渲染、复制（含 I18n 文案 fallback）、CSV 导出。

## 初始化/启动要点清单

- 确保 index.html 脚本顺序：Core → utils/services/managers → UI → main.js
- Bootstrap 分阶段执行，i18n 在 UI 之前
- 通过 window.OTA.debug 取启动报告；system-integrity-checker 将巡检依赖与注册
- MonitoringWrapper DOMContentLoaded 后包装工厂与关键实例方法

## 风险与联动改造提示（便于后续工作）

- P0 安全
  - Gemini API Key 不应内嵌前端。改为后端/函数代理或仅保留用户输入方式（无默认 key）。
  - ApiService.createOrder 使用 skipAuth:true 存疑，应改为强制带鉴权头并妥善处理 401。
- P1 架构一致性
  - 统一通过 Service Registry 获取服务；减少 window.OTA 回退路径，降低耦合。
  - 语言数组对象化转换统一走 LanguageManager，去除 Transformer/Validator/ApiService 中重复实现，避免漂移。
  - api-service.js 重复 init() 清理为单处且幂等。
- P1 隐私
  - OrderHistoryManager 本地存储包含 PII（姓名/电话/地址/邮箱）。加入“隐私模式/禁用历史/脱敏存储/保留期上限/可选加密”。
- P2 健壮性
  - Renderer 对缺失容器 DOM 注入兜底（创建面板或用户提示），避免仅日志报错。
  - FlightInfoService 生产基址/函数健康检查明确化，日志中标注环境。
- P2 观测
  - 统一错误事件/日志语义，减少多处 window.onerror 重叠处理，明确错误结构（type/code/context）。

## 附：核心数据/事件/接口“契约”摘要

- Order（前端）关键字段（局部）：customerName/Contact/Email, pickup/dropoff, pickupDate/Time, passengerCount/luggageCount, otaPrice/otaReferenceNumber, subCategoryId, carTypeId, drivingRegionId, languagesIdArray（数组）
- Order（API）关键字段（局部，snake_case）：customer_name/contact/email, pickup_location/dropoff_location, pickup_date/time, passenger_count/luggage_number, ota_price/ota_reference_number, sub_category_id, car_type_id, driving_region_id, languages_id_array（对象：{"0":"2","1":"4"...}）
- 事件：
  - multiOrderModeEnabled/Disabled、multiOrderDetected、orderSelectionChanged、batchProgressUpdated、batchProcessingComplete、multiOrderModeChanged（桥接）
- 外部接口：
  - Gemini: v1beta generateContent（文本/图片），带重试与超时
  - Gomyhire: /api（Auth/Login/CreateOrder/SystemData）
  - Netlify: /.netlify/functions/flight-info（POST）

## 脚本加载清单与顺序（实操）

- 加载顺序（必须严格遵守）：
  1) Core：js/core/*.js（container → service-locator → application-bootstrap → 其他核心）
  2) Utilities/Services/Managers：js/utils.js、js/logger.js、js/app-state.js、js/api-service.js、js/gemini-service.js、js/language-manager.js、js/paging-service-manager.js、js/order-history-manager.js、js/flight-info-service.js、js/monitoring-wrapper.js、js/currency-converter.js 等
  3) Multi-order 子系统：js/multi-order/*.js（建议按 coordinator 依赖树排列）
  4) OTA/策略/影子：js/ota-system/*、js/strategies/*、js/shadow/*、js/adapters/*
  5) UI：js/ui-manager.js、js/multi-order-manager-v2.js、其他 UI 子管理器
  6) 入口：main.js

提示：index.html 中按上述分组集中引入；禁止乱序插入，新增文件请与同组相邻。

## 服务注册表与服务键名（DI/Locator 速查）

- 注册建议键名（示例）：
  - 'logger'（getLogger）、'appState'（getAppState）
  - 'apiService'（getApiService）、'geminiService'（getGeminiService）
  - 'uiManager'（getUIManager）、'multiOrderManagerV2'（getMultiOrder）
  - 'languageManager'、'pagingServiceManager'、'orderHistoryManager'
  - 'flightInfoService'、'monitoringWrapper'
- 访问优先级：getService('name') > window.OTA.serviceLocator.getXxx() > window.OTA.Xxx（最后回退）。

## 事件矩阵（谁发/谁听）

- multiOrderModeEnabled/Disabled：发: Coordinator；听: UIManager/MultiOrderManagerV2/Renderer
- multiOrderDetected：发: Detector/Coordinator；听: Renderer/Processor
- orderSelectionChanged：发: Renderer；听: StateManager/Coordinator
- batchProgressUpdated/batchProcessingComplete：发: BatchProcessor/Processor；听: Renderer/UIManager
- multiOrderModeChanged（桥接）：发: EventBridge；听: 各 UI 模块

## 错误与重试策略（约定）

- GeminiService：
  - 超时与 429/5xx：指数退避（建议 500ms → 1s → 2s，最多 3 次）；降级到启发式拆分；记录 rateLimitContext。
  - 解析失败：尝试 JSON 修复模板；返回结构包含 {recoverable, reason, raw}。
- ApiService：
  - 401：强制刷新鉴权（禁止 skipAuth），中断创建并提示登录。
  - 4xx：返回提示信息并记录字段上下文；5xx：短退避重试 1-2 次。
- BatchProcessor：
  - 并发控制（Semaphore），失败项单独重试（最多 N 次），最终生成失败报告。

## 本地开发与验证

- 运行：直接打开 index.html，或使用任意静态服务器。
- 验证部署：npm run build（生成 deployment-validation-report.json）。
- 手动测试页面：根目录 test-*.html 与 tests/ 下 HTML，用于 UI/多订单/映射/布局回归。
- 调试：在控制台执行 window.OTA.debug.getStartupReport() / window.OTA.debug.restart()。

## 配置与环境变量

- Netlify 环境变量（仪表盘设置）：
  - FLIGHTAWARE_API_KEY（航班查询函数）
  - GEMINI_API_KEY：不应存在于前端；如需，用用户输入或后端代理传递。
  - 可选：GOMYHIRE_API_BASE（覆盖默认 API 基址），DEBUG_MODE（启用更详细日志）。
- CSP：见 netlify.toml 的 script-src/connect-src；新增外部域时需更新。

## 依赖标签（Dependency Tags）标准模板

在每个脚本文件顶部添加（模板）：

```js
/**
 * 依赖标签（Dependency Tags）
 * 文件: <相对路径>
 * 角色: <该模块的职责与边界>
 * 上游依赖(直接使用): <获取的服务/模块/全局>
 * 下游被依赖(常见调用方): <谁会调用/引用本模块>
 * 事件: <发出的事件/监听的事件/无>
 * 更新时间: YYYY-MM-DD
 */
```

约定：

- “上游/下游”尽量用模块名或服务键名；事件写清楚是否桥接。
- 修改公共行为时，同步更新本头部注释。

## 新增模块操作指南（精简）

1) 放置位置：按分层放在对应文件夹；遵守加载顺序。
2) 注册服务：window.OTA.registerService('name', factoryFn)；使用 getService('name') 解析。
3) UI 子管理器：
   window.OTA.managers = window.OTA.managers || {};
   window.OTA.managers.MyMgr = class { constructor(els){} init(){} };
4) 依赖声明：添加“依赖标签”头；在 SystemIntegrityChecker 中补充自检（可选）。

## 安全与隐私实施计划（落地步骤）

- P0 安全：
  1) 移除任何默认 GEMINI KEY；UI 留输入框或后端代理；禁止持久化。
  2) ApiService.createOrder 统一鉴权；删除 skipAuth；401 提示重新登录。
- P1 架构一致性：
  3) 统一 LanguageManager.transformForAPISync；删除重复转换实现。
  4) ApiService.init 幂等化，单处初始化；其余位置通过 DI 获取实例。
- P1 隐私：
  5) OrderHistoryManager 支持隐私模式（禁用/掩码/TTL/容量）；默认开启掩码。

## 常见问题排查（Runbook）

- 多订单面板不出现：
  - 检查 Detector 返回与 Coordinator.enableMultiOrderMode 调用；查看控制台日志。
- 创建失败：
  - 看 ApiService 返回码；是否 401（登录态）或 422（字段不完整）。
- 航班信息无响应：
  - 检查 Netlify 函数是否部署/本地 baseUrl 是否 localhost:3000；看网络面板请求。
- 卡片渲染异常：
  - 确认容器 DOM 是否存在；Renderer 有兜底或报错提示。

---

以上细节与约定可作为团队内的“开发运行手册”，后续新增模块/渠道时请按本文件流程与模板执行。

待处理：
### 1：语言格式统一

- 统一方法：LanguageManager.transformForAPISync(frontendArray) → object map
- 替换散落实现（Transformer/Validator/ApiService）为上述调用
- 回退策略：空数组 → 空对象 {}

### API 认证与错误处理约定（统一）

- 认证
  - 登录成功后保存 token 至 AppState（含 TTL）；默认 sessionStorage，不建议长期持久化
  - 所有写操作（create/update）必须携带 `Authorization: Bearer TOKEN`
  - Token 过期检测：请求前检查 TTL；过期则拒绝并提示登录

- 错误分类（type/code）
  - AUTH: UNAUTHORIZED, FORBIDDEN
  - VALIDATION: MISSING_FIELD, INVALID_FORMAT, OUT_OF_RANGE
  - NETWORK: TIMEOUT, OFFLINE, DNS
  - SERVER: RATE_LIMIT, INTERNAL_ERROR, BAD_GATEWAY
  - CLIENT: CANCELLED, ABORTED

- 错误对象最小结构
  - { type, code, status?, message, context?: { endpoint, payload?, orderId? } }

- UI 显示
  - AUTH → 弹 Toast + 高亮登录按钮
  - VALIDATION → 标注字段并展示具体提示
  - NETWORK/SERVER → Toast（可重试）+ Logger 记录详情

## LanguageManager 统一规范与范例

- 入口方法
  - getLanguagesSync(): { id:number, name:string }[]
  - getDefaultSelectionSync(): number[]
  - validateLanguageIdsSync(ids:number[]): { ok:boolean, invalid:number[] }
  - transformForAPISync(ids:number[]): { [index:string]: string }

- 范例
  - 输入： [2,4] → 输出： { "0":"2", "1":"4" }
  - 空或非法： [] → {}

- 替换点位（建议）
  - multi-order-transformer.js：语言字段组装处
  - field-mapping-validator.js：语言合法性检查处
  - api-service.js：最终提交前的 payload 规范化

## 日志与错误码规范（Logger）

- 日志级别：debug < info < warn < error
- 标准字段：{ ts, level, area, action, message, data? }
- 关键区域（area）：Auth, MultiOrder, API, Gemini, UI, FlightInfo, Transform
- 统一错误码 → UI 文案映射表（i18n）：
  - AUTH.UNAUTHORIZED → 请先登录后再创建订单
  - SERVER.RATE_LIMIT → 请求过多，请稍后重试
  - VALIDATION.MISSING_FIELD → 缺少必要字段：{field}

## 手动测试矩阵（对照现有 test-*.html）

- test-multi-order-*.html：多订单检测/渲染/批处理/健康检查
- test-field-*.html：字段映射/标准化/显示/下拉联动
- test-fliggy-*.html：渠道策略隔离
- test-ui-fixes.html：布局与交互回归
- test-airport-auto-completion.html：机场补全
- 验证建议顺序：1) 字段映射 → 2) 单订单 → 3) 多订单 → 4) 批处理 → 5) 渠道 → 6) UI 回归

## DOM/ID 快速索引（渲染依赖）

- 登录/工作区：#loginPanel, #workspace
- 订单表单：#orderForm（核心输入容器）
- 下拉字段：#ota, #carTypeId, #subCategoryId, #drivingRegionId
- 多订单面板（Renderer）：容器由 Renderer 创建并挂载到 body 尾部（缺失则兜底创建）

## CSP 扩展步骤（新增外域）

1) 在 netlify.toml 中添加 script-src/connect-src 域名
2) 如需函数代理，创建 Netlify Function 并在前端改为请求代理路径
3) 部署预览环境验证，确保浏览器无 CSP 拒绝日志

## 故障演练与回滚

- 演练：模拟 Gemini 429/超时；模拟 API 401；验证降级与提示
- 回滚：shadow-deployment.js 灰度；保留上一版本脚本组，必要时整体切回

## 迁移指南：window.OTA 直取 → DI/Service Locator

- 新代码一律使用 getService('name')
- 旧代码迁移：遇到 window.OTA.xxx 先查注册表 → 替换为 getService('xxxService')
- 过渡期保留回退，但 Logger.warn 打印迁移警告

## 性能与并发建议

- 多订单并发：默认 2-3；失败项单独重试，避免整体阻塞
- UI 渲染批处理：使用 requestAnimationFrame 合并 DOM 更新
- 长任务切分：解析/转换分片处理，避免主线程阻塞
- 资源缓存：系统静态数据（车型/区域/语言）用 AppState TTL 缓存

## Netlify Functions 本地调试

- 本地路径：`/.netlify/functions/{name}`
- 启动：使用 Netlify CLI 或 VSCode 扩展（保持与生产路径一致）
- 断点：Node 18，注意 fetch/Headers API 兼容

## 常量/枚举数据来源

- 车型/子类别/驾驶区域：ApiService.getSystemData() → AppState.staticData
- 语言列表：同上；LanguageManager 读取并校验
- 渠道映射：js/ota-system/config/prompt-templates.js 与 ota-channel-mapping.js

## 术语与缩写

- OTA：Online Travel Agency
- DI：Dependency Injection（依赖注入）
- TTL：Time To Live（存活时间）
- LRU：Least Recently Used（最近最少使用）

## 可视化关系图（Mermaid）

以下图谱可直接在支持 Mermaid 的 Markdown 预览中渲染，全面展示加载顺序、依赖关系、启动流程、事件矩阵与数据流，以及语言/认证与注册表约束。

### 分层与脚本加载顺序（必须严格遵守）

```mermaid
flowchart TD
  %% 加载顺序：Core → utils/services/managers → Multi-order → OTA/策略/影子 → UI → main.js
  subgraph L1[Core 核心（先加载）]
    DC[js/core/dependency-container.js]
    SL[js/core/service-locator.js]
    AB[js/core/application-bootstrap.js]
    UT[js/utils.js]
    LG[js/logger.js]
    AS[js/app-state.js]
    I18N[js/i18n.js]
  end

  subgraph L2[Services/Managers（再加载）]
    API[js/api-service.js]
    GEM[js/gemini-service.js]
    FIS[js/flight-info-service.js]
    CC[js/currency-converter.js]
    IMG[js/image-upload-manager.js]
    LM[js/language-manager.js]
    PSM[js/paging-service-manager.js]
    OHM[js/order-history-manager.js]
    MON[js/monitoring-wrapper.js]
  end

  subgraph L3[Multi-order 子系统]
    MOC[js/multi-order/multi-order-coordinator.js]
    MOD[js/multi-order/multi-order-detector.js]
    MOP[js/multi-order/multi-order-processor.js]
    MOR[js/multi-order/multi-order-renderer.js]
    MOS[js/multi-order/state-manager.js]
    MOT[js/multi-order/multi-order-transformer.js]
    MOB[js/multi-order/batch-processor.js]
    FMC[js/multi-order/field-mapping-config.js]
    FMV[js/multi-order/field-mapping-validator.js]
    SIC[js/multi-order/system-integrity-checker.js]
  end

  subgraph L4[OTA/策略/影子/适配]
    OTA[js/ota-system/*]
    MAP[js/ota-channel-mapping.js]
    ADP[js/adapters/*]
    STR[js/strategies/*]
    SHD[js/shadow/*]
  end

  subgraph L5[UI 层]
    UIM[js/ui-manager.js]
    MGR[js/multi-order-manager-v2.js]
  end

  subgraph L6[入口]
    MAIN[main.js]
  end

  L1 --> L2 --> L3 --> L4 --> L5 --> L6
```

### 服务与管理器依赖关系图（Service Locator/DI 优先）

```mermaid
graph LR
  %% Core
  LG[Logger] -->|提供日志| API & GEM & PSM & OHM & MON
  AS[AppState] -->|配置/缓存/鉴权| API & GEM & LM & OHM
  I18N[I18n] -->|UI 文案| UIM & OHM

  %% Services
  API[ApiService] -->|系统静态数据| UIM & MOR
  API -->|createOrder/preprocess/validate| MOP & MOB
  GEM[GeminiService] -->|解析/检测| MOD & MOP
  FIS[FlightInfoService] -->|航班查询| UIM
  CC[CurrencyConverter] --> UIM
  IMG[ImageUploadManager] --> UIM

  %% Managers
  LM[LanguageManager] -->|格式/校验/转换| API & MOP & MOT & FMV
  PSM[PagingServiceManager] -->|举牌侦测| MOT
  OHM[OrderHistoryManager] -->|保存/导出/复制| MOP & UIM
  MON[MonitoringWrapper] -->|包装工厂/性能| DC & SL & Services

  %% Multi-order
  MOC[Coordinator] --> MOD & MOP & MOR & MOB & MOS
  MOD[Detector] --> GEM & FMC
  MOP[Processor] --> GEM & API & MOT & OHM
  MOR[Renderer] --> API & DOM((DOM))
  MOS[StateManager] --> LS[(localStorage)]
  MOT[Transformer] --> FMC & FMV & PSM & LM
  MOB[BatchProcessor] --> API

  %% DI / Locator
  DC[DependencyContainer] -.注册/解析.-> API & GEM & LM & PSM & OHM & FIS & MON & UIM & MGR
  SL[ServiceLocator] -.getService().-> API & GEM & UIM & MOC & LM

  %% 外部
  GEM --- GA[generativelanguage.googleapis.com]
  API --- GMH[gomyhire.com.my/api]
  FIS --- NLF[/.netlify/functions/flight-info]
```

### 启动引导流程（Bootstrap 分阶段）

```mermaid
sequenceDiagram
  participant IDX as index.html
  participant AB as application-bootstrap.js
  participant DC as dependency-container
  participant SRV as Services
  participant MGR as Managers
  participant UI as UI + I18n
  participant FIN as Finalization

  IDX->>AB: 载入并调用启动器
  AB->>DC: 注册依赖/服务键（window.OTA.container）
  AB->>SRV: 实例化核心服务（Api/Gemini/Logger/AppState/...）
  AB->>MGR: 实例化管理器（UI/MultiOrder/Language/...）
  AB->>UI: 初始化 I18n 后渲染 UI（UIManager orchestration）
  AB->>FIN: 健康巡检 + debug 暴露（SystemIntegrityChecker）
  Note over AB,UI: window.OTA.debug { bootstrap, container, serviceLocator, getStartupReport(), restart() }
```

### 事件矩阵（发布/订阅）

```mermaid
flowchart LR
  subgraph Emitters[事件发出方]
    MOC[Coordinator]
    MOD[Detector]
    MOB[BatchProcessor]
    MOP[Processor]
    MOR[Renderer]
  end

  subgraph Events[系统事件]
    E1([multiOrderModeEnabled/Disabled])
    E2([multiOrderDetected])
    E3([orderSelectionChanged])
    E4([batchProgressUpdated])
    E5([batchProcessingComplete])
    E6([multiOrderModeChanged - 桥接])
  end

  subgraph Listeners[监听方]
    UIM[UIManager]
    MGR[multi-order-manager-v2]
    MORL[Renderer]
    MOSL[StateManager]
    MOCL[Coordinator]
  end

  MOC --> E1
  MOD --> E2
  MOR --> E3
  MOB --> E4 & E5
  MOP --> E4 & E5

  E1 --> UIM & MGR & MORL
  E2 --> MORL & MOP
  E3 --> MOSL & MOCL
  E4 --> MORL & UIM
  E5 --> MORL & UIM
  E6 --> UIM & MGR
```

### 主数据流（单订单与多订单）

```mermaid
flowchart TD
  %% 单订单
  subgraph Single[单订单主路径]
    A1[用户文本/图片] --> A2[GeminiService.parseOrder/analyzeImage]
    A2 --> A3[MultiOrder Transformer: 备用→前端→特殊→默认→一致性→API]
    A3 --> A4[ApiService.preprocess/validate]
    A4 --> A5[ApiService.createOrder]
    A5 --> A6[OrderHistoryManager.addOrder]
  end

  %% 多订单
  subgraph Multi[多订单主路径]
    B1[用户文本] --> B2[Detector: AI优先+启发式 拆分/校验]
    B2 --> B3[Coordinator.enableMultiOrderMode]
    B3 --> B4[Processor.processOrders(解析/转换/校验)]
    B4 --> B5[Renderer.showMultiOrderPanel(卡片/选择/统计)]
    B5 --> B6[Processor.createSelectedOrders(逐单API)]
    B6 --> B7[OrderHistoryManager.save]
  end

  %% 举牌派生
  subgraph Paging[举牌派生流]
    P1[PagingServiceManager.detect] --> P2[Transformer.processPagingServiceForOrder]
    P2 --> P3[标记 needs_paging_service / 生成举牌单]
  end
```

### 认证、错误与外部集成（概要）

```mermaid
flowchart LR
  subgraph Auth[认证与错误约定]
    T1[Token 保存: AppState (TTL)]
    T2[所有写操作 Authorization: Bearer TOKEN]
    T3[401 → 阻止创建/提示登录]
    E1[错误类型: AUTH/VALIDATION/NETWORK/SERVER/CLIENT]
    E2[错误对象: {type, code, status?, message, context?}]
  end

  subgraph External[外部服务/CSP]
    GA[Gemini API]
    GMH[gomyhire.com.my/api]
    NLF[Netlify Function: /flight-info]
    CSP[netlify.toml: script-src/connect-src]
  end

  API -.鉴权/重试.-> Auth
  GEM -.退避/降级.-> Auth
  API --- GMH
  GEM --- GA
  FIS --- NLF
  Auth --- CSP
```

### 语言统一与映射关键点（强约束）

```mermaid
flowchart TD
  A[LanguageManager] -->|transformForAPISync(ids:number[])| O{{{"0":"2","1":"4"}}}
  A -->|validateLanguageIdsSync| V[校验 ids 合法性]
  A -->|getLanguagesSync / getDefaultSelectionSync| L[语言列表/默认值]

  subgraph 替换点位（必须以 LM 为准）
    T1[multi-order-transformer.js]
    T2[field-mapping-validator.js]
    T3[api-service.js 提交前规范化]
  end
  A -.唯一来源.-> T1 & T2 & T3
```

### Service Registry 使用规范（统一入口）

```mermaid
flowchart LR
  DC[registerService('name', factory)] --> REG[(Registry)]
  REG --> SL[getService('name')]
  SL -->|优先| API & GEM & UIM & MOC & LM
  SL -.回退.-> W[window.OTA.*]:::warn

  classDef warn fill:#fff3cd,stroke:#f0ad4e,color:#8a6d3b;
```

## 工程级契约与实现细节（补充）

以下为关键模块的最小“契约”与实现要点，便于一致性开发与后续扩展。

### 服务契约（Services）

- ApiService（js/api-service.js）
  - 关键方法：
    - `init()` 幂等初始化；从 AppState 预热系统数据缓存（可选）。
    - `login({ email, password }) → { token, expiresAt }`：成功后写入 AppState（带 TTL）。
    - `getSystemData(force?: boolean) → { carTypes, subCategories, drivingRegions, languages }`：缓存+TTL。
    - `preprocessOrderData(orderFront) → orderFront'`：基本标准化（去空白/类型修正）。
    - `validateOrderData(orderFront) → { ok, issues[] }`：非阻断式；issues 含字段与原因。
    - `createOrder(orderApi, opts?) → { id, ... }`：必须带鉴权；401 返回 AUTH.UNAUTHORIZED。
  - 输入输出：
    - 前端订单（camelCase）→ API 订单（snake_case）。
    - 语言字段提交前统一委托 LanguageManager.transformForAPISync。
  - 错误：按文档“错误对象最小结构”返回；5xx 短退避重试 1-2 次。

- GeminiService（js/gemini-service.js）
  - 关键方法：
    - `detectAndSplitMultiOrdersWithVerification(text, opts?) → { isMulti, parts[], raw, recoverable? }`
    - `parseOrder(text, opts?) → { orderFront, confidence, recoverable, reason?, raw }`
    - `analyzeImage(image, opts?) → { orderFront|hints, confidence }`
  - 策略：429/5xx 指数退避（~0.5s/1s/2s，最多3次）；降级到启发式拆分；记录 rateLimitContext。

- LanguageManager（js/language-manager.js）
  - `getLanguagesSync() → {id,name}[]`
  - `getDefaultSelectionSync() → number[]`
  - `validateLanguageIdsSync(ids:number[]) → { ok, invalid:number[] }`
  - `transformForAPISync(ids:number[]) → { [index:string]: string }`；[] → {}。

- FlightInfoService（js/flight-info-service.js）
  - `query(params) → flightInfo | error`；通过 `/.netlify/functions/flight-info` 调用；区分本地/生产。

- MonitoringWrapper（js/monitoring-wrapper.js）
  - 包装工厂与关键异步方法：在调用前后记录 performance.mark/measure 与统一日志结构。

### 多订单子系统契约（js/multi-order/*）

- Coordinator
  - `enableMultiOrderMode(parts)`、`disableMultiOrderMode()`；转发事件：multiOrderModeEnabled/Disabled。
  - 作为调度中心持有/解析 Detector、Processor、Renderer、BatchProcessor、StateManager。

- Detector
  - `detectMultiOrder(text) → { isMulti, parts[], reason? }`：内部调用 GeminiService 优先。

- Processor
  - `processOrders(parts) → parsed[]`：对每单调用 GeminiService.parseOrder 与 Transformer/Validator。
  - `createSelectedOrders(parsed[], opts?) → { successes[], failures[] }`：并发受 BatchProcessor 控制。

- Renderer
  - `showMultiOrderPanel(parsed[], opts?)`：挂载面板到 body 尾；缺失容器时兜底创建或提示。
  - 事件：发出 orderSelectionChanged；监听批处理进度与完成事件。

- StateManager
  - 保存：会话/选择/批处理统计；定时写入 localStorage；建议命名空间化键（例如 `ota.multiOrder.*`）。

- Transformer
  - 规则顺序：备用映射 → AI → 前端 → 特殊规则 → 默认值 → 一致性 → API 格式。
  - 语言字段：强制走 LanguageManager.transformForAPISync（唯一来源）。

- BatchProcessor
  - `run(items, { concurrency=2..3, maxRetries, delayMs }) → 事件流 + 汇总`；失败项单独重试。

### AppState 与持久化（js/app-state.js）

- 键空间（建议）：
  - `auth`: { token, user, expiresAt }
  - `staticData`: { carTypes, subCategories, drivingRegions, languages, ts, ttl }
  - `config`: { debugMode, apiBase?, featureFlags? }
- 存储策略：首选 sessionStorage；带 TTL 的 get/set；读取过期自动清理。

### 本地存储（localStorage）

- MultiOrderStateManager：会话/选择/统计（命名空间化键；JSON 序列化）。
- OrderHistoryManager：按用户邮箱分桶；项结构建议 `{ payload, apiResponse, createdAt }`；支持容量上限与隐私模式（脱敏/禁用）。

### UI/DOM 合同与兜底（js/ui-manager.js + renderer）

- 关键 DOM：`#loginPanel`, `#workspace`, `#orderForm`, `#ota`, `#carTypeId`, `#subCategoryId`, `#drivingRegionId`。
- 缺失容器时的兜底：Renderer 负责注入面板或给出用户可见提示；日志等级 warn。

### 统一错误与日志结构（范例）

- 错误对象：`{ type, code, status?, message, context?: { endpoint, payload?, orderId? } }`
- 日志字段：`{ ts, level, area, action, message, data? }`；area 建议：Auth, MultiOrder, API, Gemini, UI, FlightInfo, Transform。

### 质量门禁与验证（Green before done）

- 构建/验证：`npm run build` 生成 `deployment-validation-report.json` 并检查 Netlify 发布配置。
- 手工回归：根目录 `test-*.html` 与 `tests/` 下页面；建议顺序：字段映射 → 单订单 → 多订单 → 批处理 → 渠道 → UI。
- 关键检查：
  - 脚本加载顺序是否正确；
  - Service Registry 注册/解析是否成功；
  - I18n 在 UI 之前初始化；
  - 401 授权错误是否正确提示与阻断创建；
  - 语言字段是否统一由 LanguageManager 转换；
  - 多订单面板缺失容器时是否兜底。

### OTA 渠道扩展步骤（速览）

1) 在 `js/ota-system/config/prompt-templates.js` 增加模板与映射。
2) 必要时在 `js/ota-system/*` 中添加检测/定制逻辑与渠道映射。
3) 确保 UI 与 Transformer 能处理渠道特定字段（避免污染默认路径）。
4) 回归测试：`test-fliggy-*.html` 参考，新增独立测试页。

