/**
 * 依赖标签（Dependency Tags）
 * 文件: js/strategies/fliggy-ota-strategy.js
 * 角色: 飞猪渠道策略（轻量级：提示词片段/价格计算/车型映射）
 * 上游依赖(直接使用): Logger（可选）
 * 下游被依赖(常见调用方): Gemini Integration, Customization Engine
 * 事件: 无
 * 更新时间: 2025-08-09
 */
/**
 * Fliggy OTA策略 - 纯静态提示词片段提供者
 *
 * 设计理念：策略文件只提供渠道特定的提示词片段，不承担任何数据处理职责
 * 核心职责：为AI提供Fliggy渠道特定的字段级提示词片段
 *
 * 不再继承BaseOTAStrategy，成为纯静态工具类
 */
class FliggyOTAStrategy {

    /**
     * 获取策略的渠道名称
     * @returns {string} 渠道名称
     */
    static getChannelName() {
        return 'fliggy';
    }


    /**
     * 从文本内容检测Fliggy渠道特征
     * 供渠道检测器调用的静态方法
     * @param {string} text - 输入文本
     * @returns {object} 检测结果 {detectedChannel, confidence, details}
     */
    static detectFromContent(text) {
        const result = {
            detectedChannel: null,
            confidence: 0,
            details: []
        };

        if (!text || typeof text !== 'string') {
            return result;
        }

        // 检测"订单编号" + 19位数字的模式（Fliggy特征）
        const fliggyPattern = /订单编号[：:\s]*\d{19}/g;
        const matches = text.match(fliggyPattern);

        if (matches && matches.length > 0) {
            result.detectedChannel = 'fliggy'; // 使用小写，与策略注册键一致
            result.confidence = 0.95;
            result.details.push({
                type: 'fliggy_order_pattern',
                pattern: '订单编号+19位数字',
                matches: matches,
                count: matches.length,
                description: 'Fliggy订单编号特征识别'
            });
        }

        return result;
    }

    /**
     * 获取字段级提示词片段（单渠道单文件）
     * 仅做抽取/格式指引，不重复引擎中的业务处理
     * @param {{isMultiOrder?: boolean}} _ctx - 上下文参数（当前未使用，保留用于未来扩展）
     * @returns {Record<string, string>}
     */
    static getFieldPromptSnippets(_ctx = {}) {
        return {
            // 渠道名称固定返回
            ota: '渠道识别：请识别这是飞猪(Fliggy)渠道的订单，输出JSON时ota字段请设置为"Fliggy"。',
            // 价格与车型ID映射片段
            ota_price: '价格识别与换算：请识别"商家实收"数值作为订单价格；识别订单所属地区，若为马来西亚，最终价格=商家实收×0.84×0.615；若为新加坡，最终价格=商家实收×0.84×0.2；均保留两位小数，输出最终价；无法确定地区时仅输出基础价并标注原因，不要猜测。',
            car_type_id: '车型ID映射（根据中文车型名称）：请从订单文本中识别车型名称，然后按以下映射返回对应ID：经济型/舒适型/五座→5；经济型/舒适型/七座→35；商务七座/商务型七座→31；豪华七座/豪华型七座→32；商务九座/商务型九座→20；中巴/小巴→24。输出JSON时请设置car_type_id为上述ID之一的整数，若无法识别车型名称则返回null，不要猜测或输出名称。'
        };
    }

    /**
     * 统一车型映射（名称→ID）
     * @returns {Object} 车型ID映射
     */
    static getVehicleIdMapping() {
        return {
            economy_comfort_5: 5,     // 经济/舒适 五座
            economy_comfort_7: 35,    // 经济/舒适 七座
            business_7: 31,           // 商务七座
            luxury_7: 32,             // 豪华七座
            business_9: 20,           // 商务九座
            minibus: 24               // 中巴
        };
    }
}

// 导出
if (typeof module !== 'undefined' && module.exports) {
    module.exports = FliggyOTAStrategy;
} else if (typeof window !== 'undefined') {
    window.FliggyOTAStrategy = FliggyOTAStrategy;
}

console.log('✅ FliggyOTAStrategy (重构版) 已加载');
