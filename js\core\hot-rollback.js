/**
 * 热回滚机制 - 即时回滚能力
 * 
 * 设计目标：
 * - 提供即时回滚到稳定版本的能力
 * - 监控系统健康状态，自动触发回滚
 * - 保存系统状态快照，支持精确恢复
 * - 零中断回滚操作
 * 
 * <AUTHOR>
 * @version 1.0.0
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};
window.OTA.core = window.OTA.core || {};

(function() {
    'use strict';

    /**
     * 热回滚管理器
     * 管理系统状态快照和回滚操作
     */
    class HotRollback {
        constructor() {
            this.snapshots = new Map();
            this.currentSnapshot = null;
            this.rollbackHistory = [];
            this.healthChecks = new Map();
            this.autoRollbackEnabled = true;
            this.logger = this.getLogger();
            this.featureToggle = window.OTA.featureToggle;
            
            // 初始化健康检查
            this.initializeHealthChecks();
            
            this.logger.log('热回滚管理器已初始化');
        }

        /**
         * 创建系统状态快照
         * @param {string} snapshotName - 快照名称
         * @param {string} description - 快照描述
         * @returns {string} 快照ID
         */
        createSnapshot(snapshotName, description = '') {
            const snapshotId = `snapshot_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
            
            const snapshot = {
                id: snapshotId,
                name: snapshotName,
                description,
                timestamp: Date.now(),
                systemState: this.captureSystemState(),
                featureToggles: this.captureFeatureToggles(),
                configurations: this.captureConfigurations(),
                metadata: {
                    userAgent: navigator.userAgent,
                    url: window.location.href,
                    timestamp: new Date().toISOString()
                }
            };

            this.snapshots.set(snapshotId, snapshot);
            this.currentSnapshot = snapshotId;
            
            this.logger.log(`系统快照已创建: ${snapshotName} (${snapshotId})`);
            
            return snapshotId;
        }

        /**
         * 捕获系统状态
         * @returns {Object} 系统状态
         */
        captureSystemState() {
            const state = {
                managers: {},
                services: {},
                components: {}
            };

            try {
                // 捕获Manager状态
                if (window.OTA) {
                    ['formManager', 'eventManager', 'uiManager', 'otaManager'].forEach(managerName => {
                        if (window.OTA[managerName]) {
                            state.managers[managerName] = {
                                initialized: window.OTA[managerName].initialized || false,
                                name: window.OTA[managerName].getName ? window.OTA[managerName].getName() : managerName
                            };
                        }
                    });

                    // 捕获服务状态
                    if (window.OTA.container) {
                        state.services.containerInitialized = window.OTA.container.isInitialized();
                    }
                }

                // 捕获DOM状态（关键元素）
                state.dom = {
                    hasLoginForm: !!document.getElementById('loginForm'),
                    hasOrderForm: !!document.getElementById('orderForm'),
                    hasMultiOrderPanel: !!document.querySelector('.multi-order-panel')
                };

            } catch (error) {
                this.logger.log('捕获系统状态时出错', 'error', error);
            }

            return state;
        }

        /**
         * 捕获特性开关状态
         * @returns {Object} 特性开关状态
         */
        captureFeatureToggles() {
            try {
                if (this.featureToggle) {
                    return this.featureToggle.getAllFeatures();
                }
                return {};
            } catch (error) {
                this.logger.log('捕获特性开关状态时出错', 'error', error);
                return {};
            }
        }

        /**
         * 捕获配置状态
         * @returns {Object} 配置状态
         */
        captureConfigurations() {
            const configs = {};

            try {
                // 捕获OTA配置
                if (window.OTA && window.OTA.config) {
                    configs.ota = { ...window.OTA.config };
                }

                // 捕获localStorage配置
                configs.localStorage = {};
                for (let i = 0; i < localStorage.length; i++) {
                    const key = localStorage.key(i);
                    if (key && key.startsWith('ota-')) {
                        configs.localStorage[key] = localStorage.getItem(key);
                    }
                }

            } catch (error) {
                this.logger.log('捕获配置状态时出错', 'error', error);
            }

            return configs;
        }

        /**
         * 执行回滚到指定快照
         * @param {string} snapshotId - 快照ID
         * @returns {Promise<boolean>} 回滚是否成功
         */
        async rollbackToSnapshot(snapshotId) {
            const snapshot = this.snapshots.get(snapshotId);
            if (!snapshot) {
                throw new Error(`快照不存在: ${snapshotId}`);
            }

            this.logger.log(`开始回滚到快照: ${snapshot.name} (${snapshotId})`);

            try {
                // 记录回滚操作
                const rollbackRecord = {
                    timestamp: Date.now(),
                    fromSnapshot: this.currentSnapshot,
                    toSnapshot: snapshotId,
                    reason: 'manual',
                    success: false
                };

                // 1. 恢复特性开关
                await this.restoreFeatureToggles(snapshot.featureToggles);

                // 2. 恢复配置
                await this.restoreConfigurations(snapshot.configurations);

                // 3. 触发系统重新初始化（如果需要）
                await this.triggerSystemReinit(snapshot.systemState);

                // 4. 验证回滚结果
                const isValid = await this.validateRollback(snapshot);

                if (isValid) {
                    this.currentSnapshot = snapshotId;
                    rollbackRecord.success = true;
                    this.logger.log(`回滚成功: ${snapshot.name}`);
                } else {
                    throw new Error('回滚验证失败');
                }

                this.rollbackHistory.push(rollbackRecord);
                return true;

            } catch (error) {
                this.logger.log(`回滚失败: ${error.message}`, 'error');
                
                // 记录失败的回滚
                this.rollbackHistory.push({
                    timestamp: Date.now(),
                    fromSnapshot: this.currentSnapshot,
                    toSnapshot: snapshotId,
                    reason: 'manual',
                    success: false,
                    error: error.message
                });

                throw error;
            }
        }

        /**
         * 恢复特性开关
         * @param {Object} featureToggles - 特性开关配置
         */
        async restoreFeatureToggles(featureToggles) {
            if (this.featureToggle && featureToggles) {
                this.featureToggle.setFeatures(featureToggles, true);
                this.logger.log('特性开关已恢复');
            }
        }

        /**
         * 恢复配置
         * @param {Object} configurations - 配置对象
         */
        async restoreConfigurations(configurations) {
            try {
                // 恢复localStorage配置
                if (configurations.localStorage) {
                    Object.entries(configurations.localStorage).forEach(([key, value]) => {
                        localStorage.setItem(key, value);
                    });
                }

                // 恢复OTA配置
                if (configurations.ota && window.OTA) {
                    window.OTA.config = { ...configurations.ota };
                }

                this.logger.log('配置已恢复');
            } catch (error) {
                this.logger.log('恢复配置时出错', 'error', error);
            }
        }

        /**
         * 触发系统重新初始化
         * @param {Object} systemState - 系统状态
         */
        async triggerSystemReinit(systemState) {
            // 这里可以根据需要触发特定组件的重新初始化
            // 目前采用保守策略，只触发事件通知
            if (window.OTA && window.OTA.eventCoordinator) {
                window.OTA.eventCoordinator.emit('system-rollback', {
                    timestamp: Date.now(),
                    targetState: systemState
                });
            }
        }

        /**
         * 验证回滚结果
         * @param {Object} snapshot - 目标快照
         * @returns {Promise<boolean>} 验证是否通过
         */
        async validateRollback(snapshot) {
            try {
                // 简单验证：检查关键组件是否正常
                const currentState = this.captureSystemState();
                
                // 验证DOM结构
                if (snapshot.systemState.dom) {
                    const domValid = Object.entries(snapshot.systemState.dom).every(([key, expectedValue]) => {
                        const currentValue = currentState.dom[key];
                        return currentValue === expectedValue;
                    });
                    
                    if (!domValid) {
                        this.logger.log('DOM状态验证失败', 'warn');
                        return false;
                    }
                }

                return true;
            } catch (error) {
                this.logger.log('回滚验证时出错', 'error', error);
                return false;
            }
        }

        /**
         * 初始化健康检查
         */
        initializeHealthChecks() {
            // 基础健康检查
            this.healthChecks.set('basic', () => {
                return {
                    healthy: true,
                    message: 'System is running'
                };
            });

            // JavaScript错误检查
            this.healthChecks.set('jsErrors', () => {
                const errorCount = window.OTA?.errorCount || 0;
                return {
                    healthy: errorCount < 5,
                    message: `JavaScript errors: ${errorCount}`
                };
            });
        }

        /**
         * 执行健康检查
         * @returns {Object} 健康检查结果
         */
        performHealthCheck() {
            const results = {};
            let overallHealthy = true;

            this.healthChecks.forEach((check, name) => {
                try {
                    const result = check();
                    results[name] = result;
                    if (!result.healthy) {
                        overallHealthy = false;
                    }
                } catch (error) {
                    results[name] = {
                        healthy: false,
                        message: `Health check failed: ${error.message}`
                    };
                    overallHealthy = false;
                }
            });

            return {
                overall: overallHealthy,
                checks: results,
                timestamp: Date.now()
            };
        }

        /**
         * 紧急回滚到最后一个稳定快照
         */
        async emergencyRollback() {
            this.logger.log('执行紧急回滚', 'warn');

            // 查找最后一个稳定快照
            const stableSnapshots = Array.from(this.snapshots.values())
                .filter(s => s.name.includes('stable') || s.name.includes('production'))
                .sort((a, b) => b.timestamp - a.timestamp);

            if (stableSnapshots.length > 0) {
                await this.rollbackToSnapshot(stableSnapshots[0].id);
            } else {
                // 如果没有稳定快照，启用安全模式
                if (this.featureToggle) {
                    this.featureToggle.enableSafeMode();
                }
                this.logger.log('未找到稳定快照，已启用安全模式', 'warn');
            }
        }

        /**
         * 获取快照列表
         * @returns {Array} 快照列表
         */
        getSnapshots() {
            return Array.from(this.snapshots.values()).sort((a, b) => b.timestamp - a.timestamp);
        }

        /**
         * 删除快照
         * @param {string} snapshotId - 快照ID
         */
        deleteSnapshot(snapshotId) {
            if (this.snapshots.delete(snapshotId)) {
                this.logger.log(`快照已删除: ${snapshotId}`);
            }
        }

        /**
         * 获取Logger实例
         * @returns {Object} Logger实例
         */
        getLogger() {
            try {
                if (typeof getLogger === 'function') {
                    return getLogger();
                }
                return {
                    log: (message, level = 'info', data = null) => {
                        console.log(`[HotRollback][${level.toUpperCase()}] ${message}`, data || '');
                    }
                };
            } catch (error) {
                return {
                    log: (message, level = 'info', data = null) => {
                        console.log(`[HotRollback][${level.toUpperCase()}] ${message}`, data || '');
                    }
                };
            }
        }
    }

    // 创建全局实例
    const hotRollback = new HotRollback();

    // 暴露到OTA命名空间
    window.OTA.core.HotRollback = HotRollback;
    window.OTA.hotRollback = hotRollback;

    // 减法开发：仅在开启安全模式或回滚相关特性时创建快照
    try {
        const ft = window.OTA?.featureToggle;
        const shouldSnapshot = ft?.isEnabled('enableEmergencyRollback') || ft?.isEnabled('enableSafeMode');
        if (shouldSnapshot) {
            hotRollback.createSnapshot('initial', '系统初始状态快照');
        } else {
            console.log('🧯 热回滚已加载（未创建初始快照，受特性开关控制）');
        }
    } catch (_) {
        // 忽略错误，避免影响主流程
    }

    console.log('✅ 热回滚机制已加载');

})();
