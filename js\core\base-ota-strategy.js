/**
 * OTA策略基类
 * 
 * 定义所有OTA策略的通用接口和基础功能
 * 每个具体的OTA通道策略都需要继承此基类
 * 
 * @abstract
 */
class BaseOTAStrategy {
    constructor(channelName) {
        if (this.constructor === BaseOTAStrategy) {
            throw new Error('BaseOTAStrategy is abstract and cannot be instantiated directly');
        }
        
        this.channelName = channelName;
        this.isActive = false;
        this.configManager = null;
        this.eventEmitter = null;
        this.configuration = {};
        this.metadata = {};
        
        // 策略特定的日志标识
        this.logPrefix = `[${channelName}Strategy]`;
    }

    /**
     * 设置配置管理器
     * @param {OTAConfigurationManager} configManager 
     */
    setConfigManager(configManager) {
        this.configManager = configManager;
        this.loadConfiguration();
    }

    /**
     * 设置事件发射器
     * @param {OTAEventBridge} eventEmitter 
     */
    setEventEmitter(eventEmitter) {
        this.eventEmitter = eventEmitter;
    }

    /**
     * 激活策略
     * @param {Object} metadata - 激活时的元数据
     */
    activate(metadata = {}) {
        this.log('Activating strategy...');
        
        this.metadata = { ...metadata };
        this.isActive = true;
        
        // 调用子类的激活逻辑
        this.onActivate(metadata);
        
        // 发出激活事件
        this.emit('strategy-activated', {
            channel: this.channelName,
            metadata: this.metadata
        });
        
        this.log('Strategy activated');
    }

    /**
     * 停用策略
     */
    deactivate() {
        if (!this.isActive) return;
        
        this.log('Deactivating strategy...');
        
        // 调用子类的停用逻辑
        this.onDeactivate();
        
        this.isActive = false;
        this.metadata = {};
        
        // 发出停用事件
        this.emit('strategy-deactivated', {
            channel: this.channelName
        });
        
        this.log('Strategy deactivated');
    }

    /**
     * 获取渠道特定的提示词片段
     * 策略的核心职责：提供渠道特定的AI提示词片段
     * 注意：此方法应该在子类中作为静态方法实现
     * @returns {Object} 字段级提示词片段映射
     */
    getFieldPromptSnippets() {
        // 默认返回空对象，子类应该重写此方法或提供静态版本
        return {};
    }

    /**
     * 获取策略配置
     * @returns {Object} 当前配置
     */
    getConfiguration() {
        return { ...this.configuration };
    }

    /**
     * 更新策略配置
     * @param {Object} newConfig - 新配置
     */
    updateConfiguration(newConfig) {
        this.configuration = { ...this.configuration, ...newConfig };
        this.onConfigurationUpdated(newConfig);
        
        this.emit('configuration-updated', {
            channel: this.channelName,
            configuration: this.configuration
        });
    }

    /**
     * 加载配置
     * @protected
     */
    loadConfiguration() {
        if (this.configManager) {
            this.configuration = this.configManager.getChannelConfiguration(this.channelName);
        }
    }

    /**
     * 获取策略的渠道名称
     * @returns {string} 渠道名称
     */
    getChannelName() {
        return this.channelName;
    }

    /**
     * 策略激活时的回调 - 子类可重写
     * @param {Object} metadata - 激活元数据
     * @protected
     */
    onActivate(metadata) {
        // 子类可重写此方法
    }

    /**
     * 策略停用时的回调 - 子类可重写
     * @protected
     */
    onDeactivate() {
        // 子类可重写此方法
    }

    /**
     * 配置更新时的回调 - 子类可重写
     * @param {Object} newConfig - 新配置
     * @protected
     */
    onConfigurationUpdated(newConfig) {
        // 子类可重写此方法
    }

    /**
     * 发出事件
     * @param {string} eventName - 事件名称
     * @param {Object} data - 事件数据
     * @protected
     */
    emit(eventName, data = {}) {
        if (this.eventEmitter) {
            this.eventEmitter.emit(eventName, {
                ...data,
                source: this.channelName,
                timestamp: Date.now()
            });
        }
    }

    /**
     * 记录日志
     * @param {...any} args - 日志参数
     * @protected
     */
    log(...args) {
        console.log(this.logPrefix, ...args);
    }

    /**
     * 记录警告
     * @param {...any} args - 警告参数
     * @protected
     */
    warn(...args) {
        console.warn(this.logPrefix, ...args);
    }

    /**
     * 记录错误
     * @param {...any} args - 错误参数
     * @protected
     */
    error(...args) {
        console.error(this.logPrefix, ...args);
    }

    /**
     * 获取策略信息
     * @returns {Object} 策略信息
     */
    getInfo() {
        return {
            channelName: this.channelName,
            isActive: this.isActive,
            hasConfiguration: Object.keys(this.configuration).length > 0,
            configurationKeys: Object.keys(this.configuration),
            metadata: { ...this.metadata }
        };
    }
}

/**
 * 默认OTA策略
 * 处理无特定通道或未识别通道的情况
 */
class DefaultOTAStrategy extends BaseOTAStrategy {
    constructor() {
        super('default');
    }

    /**
     * 获取默认策略的提示词片段
     * @returns {Object} 空的提示词片段映射
     */
    getFieldPromptSnippets() {
        // 默认策略不提供特殊的提示词片段
        return {};
    }
}

// 导出
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { BaseOTAStrategy, DefaultOTAStrategy };
} else if (typeof window !== 'undefined') {
    window.BaseOTAStrategy = BaseOTAStrategy;
    window.DefaultOTAStrategy = DefaultOTAStrategy;
}
