<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fliggy检测测试</title>
</head>
<body>
    <h1>Fliggy订单检测测试</h1>
    
    <div id="results"></div>

    <script>
        // 模拟基础OTA策略类
        class BaseOTAStrategy {
            constructor(name) {
                this.name = name;
            }
        }
        
        // 加载必要的脚本
        function loadScript(src) {
            return new Promise((resolve, reject) => {
                const script = document.createElement('script');
                script.src = src;
                script.onload = resolve;
                script.onerror = reject;
                document.head.appendChild(script);
            });
        }

        async function runTest() {
            const resultsDiv = document.getElementById('results');
            
            try {
                // 加载依赖脚本
                await loadScript('js/ota-system/ota-channel-detector.js');
                await loadScript('js/strategies/fliggy-ota-strategy.js');
                
                const testData = `订单编号：4673826324087252736买家：wujiexian124支付时间：2025-08-08 15:39:30
查看详情

经济7座

【送机】

新加坡-新加坡

[出发]新加坡庄家大酒店

[抵达]樟宜机场

约23.4公里

2025-08-10 04:00:00

吴洁娴

真实号：13771817391

---
3成人0儿童

司机姓名：---

司机电话：---

---

总价格：318元

用户实付：318.00元

商家实收：318元

待派单

14:21:51`;

                // 测试渠道检测
                if (window.OTA && window.OTA.channelDetector) {
                    const detection = window.OTA.channelDetector.detectChannel(testData);
                    
                    resultsDiv.innerHTML = `
                        <h2>渠道检测结果:</h2>
                        <p><strong>检测到的渠道:</strong> ${detection.detectedChannel || '未检测到'}</p>
                        <p><strong>置信度:</strong> ${detection.confidence}</p>
                        <p><strong>检测方法:</strong> ${detection.method}</p>
                        <p><strong>详细信息:</strong> ${JSON.stringify(detection.details, null, 2)}</p>
                        
                        <h2>Fliggy策略字段片段:</h2>
                        <pre>${JSON.stringify(FliggyOTAStrategy.getFieldPromptSnippets(), null, 2)}</pre>
                    `;
                } else {
                    resultsDiv.innerHTML = '<p style="color: red;">OTA检测器未加载</p>';
                }
                
            } catch (error) {
                resultsDiv.innerHTML = `<p style="color: red;">错误: ${error.message}</p>`;
            }
        }

        // 页面加载后运行测试
        window.addEventListener('load', runTest);
    </script>
</body>
</html>