/**
 * @OTA_GEMINI_INTEGRATION OTA系统与Gemini服务集成
 * 🏷️ 标签: @OTA_GEMINI_INTEGRATION
 * 📝 说明: 将OTA渠道识别和定制化功能集成到Gemini服务中
 * <AUTHOR>
 * @version 1.0.0
 */

(function() {
    'use strict';

    // 确保OTA命名空间存在
    window.OTA = window.OTA || {};

    /**
     * OTA与Gemini服务集成器
     */
    class OTAGeminiIntegration {
        constructor() {
            this.logger = this.getLogger();
            this.isInitialized = false;
            this.integrationCallbacks = new Map();
        }

        /**
         * 初始化集成
         * @returns {boolean} 初始化是否成功
         */
        initialize() {
            try {
                // 检查依赖服务是否可用
                if (!this.checkDependencies()) {
                    throw new Error('Required services not available');
                }

                // 注册Gemini服务钩子
                this.registerGeminiHooks();
                
                // 扩展现有Gemini服务
                this.extendGeminiService();

                this.isInitialized = true;
                this.logger.log('OTA-Gemini集成初始化成功', 'info');
                return true;

            } catch (error) {
                this.logger.log('OTA-Gemini集成初始化失败', 'error', { error: error.message });
                return false;
            }
        }

        /**
         * 检查依赖服务
         * @returns {boolean} 依赖是否满足
         */
        checkDependencies() {
            const required = [
                'window.OTA.channelDetector',
                'window.OTA.customizationEngine', 
                'window.OTA.generateChannelPrompt'
            ];

            for (const dependency of required) {
                if (!this.getNestedProperty(window, dependency)) {
                    this.logger.log(`缺少依赖: ${dependency}`, 'error');
                    return false;
                }
            }

            return true;
        }

        /**
         * 注册Gemini服务钩子
         */
        registerGeminiHooks() {
            // 在订单解析前进行渠道检测
            this.addPreProcessingHook('channelDetection', (text, context) => {
                return this.performChannelDetection(text, context);
            });

            // 在生成提示词时应用渠道定制
            this.addPromptHook('channelCustomization', (basePrompt, context) => {
                return this.customizePromptForChannel(basePrompt, context);
            });

            // 在订单处理后应用渠道特定逻辑
            this.addPostProcessingHook('channelProcessing', (orderData, context) => {
                return this.processOrderForChannel(orderData, context);
            });
        }

        /**
         * 扩展现有Gemini服务
         */
        extendGeminiService() {
            // 获取Gemini服务实例
            const geminiService = this.getGeminiService();
            if (!geminiService) {
                throw new Error('Gemini service not found');
            }

            // 保存原始方法
            const originalParseOrder = geminiService.parseOrder;
            const originalParseOrderText = geminiService.parseOrderText;
            const originalGenerateMultiOrderAnalysis = geminiService.generateMultiOrderAnalysis;

            // 扩展parseOrder方法（实时分析使用的主要方法）
            geminiService.parseOrder = async function(text, isRealtime = false) {
                try {
                    // 执行渠道检测
                    const detectionResult = window.OTA.otaGeminiIntegration.performChannelDetection(text, { isRealtime });
                    
                    // 如果检测到渠道，使用定制化的提示词调用
                    if (detectionResult.detectedChannel) {
                        window.OTA.otaGeminiIntegration.logger.log('使用渠道定制化解析', 'info', {
                            渠道: detectionResult.detectedChannel,
                            置信度: detectionResult.confidence
                        });
                        
                        // 调用带渠道定制的解析方法
                        return await window.OTA.otaGeminiIntegration.parseOrderWithChannelCustomization(
                            text, 
                            isRealtime, 
                            detectionResult,
                            originalParseOrder.bind(this)
                        );
                    } else {
                        // 没有检测到渠道，使用原始方法
                        return await originalParseOrder.call(this, text, isRealtime);
                    }
                    
                } catch (error) {
                    window.OTA.otaGeminiIntegration.logger.log('OTA增强parseOrder失败，回退到原始方法', 'warning', { error: error.message });
                    return await originalParseOrder.call(this, text, isRealtime);
                }
            };

            // 扩展单订单解析方法
            geminiService.parseOrderText = async function(text, isRealTime = false) {
                try {
                    // 执行渠道检测
                    const detectionResult = window.OTA.otaGeminiIntegration.performChannelDetection(text, { isRealTime });
                    
                    // 获取定制化提示词
                    const context = { 
                        detectedChannel: detectionResult.detectedChannel,
                        isRealTime,
                        isMultiOrder: false 
                    };
                    
                    // 调用原始方法，但使用定制化的上下文
                    const result = await originalParseOrderText.call(this, text, isRealTime);
                    
                    // 应用渠道特定处理
                    if (result && detectionResult.detectedChannel) {
                        return window.OTA.otaGeminiIntegration.processOrderForChannel(result, context);
                    }
                    
                    return result;
                    
                } catch (error) {
                    window.OTA.otaGeminiIntegration.logger.log('OTA增强解析失败，回退到原始方法', 'warning', { error: error.message });
                    return await originalParseOrderText.call(this, text, isRealTime);
                }
            };

            // 扩展多订单分析方法
            geminiService.generateMultiOrderAnalysis = async function(text) {
                try {
                    // 执行渠道检测
                    const detectionResult = window.OTA.otaGeminiIntegration.performChannelDetection(text, { isMultiOrder: true });
                    
                    // 获取定制化提示词和上下文
                    const context = { 
                        detectedChannel: detectionResult.detectedChannel,
                        isMultiOrder: true 
                    };
                    
                    // 调用原始方法
                    const result = await originalGenerateMultiOrderAnalysis.call(this, text);
                    
                    // 对每个订单应用渠道特定处理
                    if (result && result.orders && detectionResult.detectedChannel) {
                        result.orders = result.orders.map(order => 
                            window.OTA.otaGeminiIntegration.processOrderForChannel(order, context)
                        );
                    }
                    
                    return result;
                    
                } catch (error) {
                    window.OTA.otaGeminiIntegration.logger.log('OTA增强多订单分析失败，回退到原始方法', 'warning', { error: error.message });
                    return await originalGenerateMultiOrderAnalysis.call(this, text);
                }
            };

            this.logger.log('Gemini服务扩展完成', 'info');
        }

        /**
         * 执行渠道检测
         * @param {string} text - 输入文本
         * @param {object} context - 上下文信息
         * @returns {object} 检测结果
         */
        performChannelDetection(text, context = {}) {
            try {
                // 从文本中提取可能的参考号
                const referenceNumber = this.extractReferenceNumber(text);
                
                // 执行渠道检测
                const detectionResult = window.OTA.channelDetector.detectChannel(
                    text,
                    referenceNumber,
                    context
                );

                this.logger.log('渠道检测完成', 'info', {
                    检测到的渠道: detectionResult.detectedChannel,
                    置信度: detectionResult.confidence,
                    方法: detectionResult.method
                });

                return detectionResult;

            } catch (error) {
                this.logger.log('渠道检测失败', 'error', { error: error.message });
                return {
                    detectedChannel: null,
                    confidence: 0,
                    method: null,
                    error: error.message
                };
            }
        }

        /**
         * 为渠道定制提示词
         * @param {string} basePrompt - 基础提示词
         * @param {object} context - 上下文信息
         * @returns {string} 定制化提示词
         */
        customizePromptForChannel(basePrompt, context) {
            try {
                const { detectedChannel, isMultiOrder } = context || {};

                // 若未检测到渠道或不存在模板生成器，直接返回基础提示词
                if (!detectedChannel || !window.OTA.generateChannelPrompt) {
                    return basePrompt;
                }

                // 可选特性开关：字段级提示词片段注入
                const useFieldSnippets = !!(window.OTA?.featureToggle?.isEnabled?.('enableFieldPromptSnippets'));

                // 从对应渠道策略获取字段级片段（单渠道单文件：如 FliggyOTAStrategy）
                let fieldPromptSnippets = undefined;
                if (useFieldSnippets) {
                    try {
                        const strategyClassName = `${detectedChannel}OTAStrategy`; // 例如 'Fliggy' → 'FliggyOTAStrategy'
                        const StrategyClass = window[strategyClassName];
                        if (StrategyClass && typeof StrategyClass.getFieldPromptSnippets === 'function') {
                            fieldPromptSnippets = StrategyClass.getFieldPromptSnippets({ isMultiOrder });
                        }
                    } catch (_) { /* 忽略片段注入失败，回退为无片段 */ }
                }

                // 生成最终提示词（支持字段片段可选注入）
                const customPrompt = window.OTA.generateChannelPrompt(
                    detectedChannel,
                    basePrompt,
                    isMultiOrder,
                    { fieldPromptSnippets }
                );

                // 记录事件用于后续统计
                try {
                    window.OTA?.eventCoordinator?.emit('prompt-customized', {
                        channel: detectedChannel,
                        isMultiOrder,
                        baseLength: basePrompt?.length || 0,
                        finalLength: customPrompt?.length || 0,
                        fieldSnippetsInjected: !!fieldPromptSnippets,
                        timestamp: Date.now()
                    });
                } catch (_) { /* 忽略事件失败 */ }

                this.logger.log('提示词定制化完成', 'info', {
                    渠道: detectedChannel,
                    是否多订单: isMultiOrder,
                    原始长度: basePrompt.length,
                    定制后长度: customPrompt.length,
                    片段注入: !!fieldPromptSnippets
                });

                return customPrompt;

            } catch (error) {
                this.logger.log('提示词定制化失败', 'error', { error: error.message });
                return basePrompt;
            }
        }

        /**
         * 使用渠道定制化解析订单
         * @param {string} text - 订单文本
         * @param {boolean} isRealtime - 是否实时模式
         * @param {object} detectionResult - 渠道检测结果
         * @param {function} originalMethod - 原始解析方法
         * @returns {object} 解析结果
         */
        async parseOrderWithChannelCustomization(text, isRealtime, detectionResult, originalMethod) {
            try {
                const { detectedChannel } = detectionResult;
                
                // 启用字段级提示词片段注入
                const useFieldSnippets = true; // 强制启用，不依赖特性开关
                
                // 从对应渠道策略获取字段级片段
                let fieldPromptSnippets = undefined;
                if (useFieldSnippets) {
                    try {
                        const strategyClassName = `${detectedChannel}OTAStrategy`;
                        const StrategyClass = window[strategyClassName];
                        if (StrategyClass && typeof StrategyClass.getFieldPromptSnippets === 'function') {
                            fieldPromptSnippets = StrategyClass.getFieldPromptSnippets({ 
                                isMultiOrder: false,
                                isRealtime 
                            });
                            
                            this.logger.log('字段提示词片段已获取', 'info', {
                                策略类: strategyClassName,
                                片段数量: Object.keys(fieldPromptSnippets || {}).length
                            });
                        }
                    } catch (error) {
                        this.logger.log('获取字段提示词片段失败', 'warning', { error: error.message });
                    }
                }
                
                // 如果有字段片段，需要修改Gemini服务的提示词构建过程
                if (fieldPromptSnippets && Object.keys(fieldPromptSnippets).length > 0) {
                    // 临时覆盖提示词模板以注入字段片段
                    return await this.callWithInjectedPrompt(text, isRealtime, detectedChannel, fieldPromptSnippets, originalMethod);
                } else {
                    // 没有字段片段，直接调用原始方法
                    return await originalMethod(text, isRealtime);
                }
                
            } catch (error) {
                this.logger.log('渠道定制化解析失败', 'error', { error: error.message });
                // 回退到原始方法
                return await originalMethod(text, isRealtime);
            }
        }

        /**
         * 用注入字段提示词的方式调用解析
         * @param {string} text - 订单文本
         * @param {boolean} isRealtime - 是否实时模式
         * @param {string} detectedChannel - 检测到的渠道
         * @param {object} fieldPromptSnippets - 字段提示词片段
         * @param {function} originalMethod - 原始方法
         * @returns {object} 解析结果
         */
        async callWithInjectedPrompt(text, isRealtime, detectedChannel, fieldPromptSnippets, originalMethod) {
            // 获取Gemini服务实例
            const geminiService = this.getGeminiService();
            if (!geminiService) {
                throw new Error('Gemini service not found');
            }
            
            // 备份原始的提示词模板
            const originalPromptTemplates = geminiService.promptTemplates;
            
            try {
                // 创建增强的提示词模板
                const enhancedPromptTemplates = JSON.parse(JSON.stringify(originalPromptTemplates));
                
                // 在系统提示词中注入字段级片段
                const fieldInstructions = Object.entries(fieldPromptSnippets)
                    .map(([field, instruction], index) => `${index + 1}. [${field}] ${instruction}`)
                    .join('\n');
                
                enhancedPromptTemplates.systemRole.baseInstructions += `\n\n**${detectedChannel}渠道专属字段处理规则：**\n${fieldInstructions}`;
                
                // 临时替换提示词模板
                geminiService.promptTemplates = enhancedPromptTemplates;
                
                this.logger.log('字段提示词已注入到系统提示词', 'info', {
                    渠道: detectedChannel,
                    字段数量: Object.keys(fieldPromptSnippets).length
                });
                
                // 调用原始方法
                const result = await originalMethod(text, isRealtime);
                
                return result;
                
            } finally {
                // 恢复原始提示词模板
                geminiService.promptTemplates = originalPromptTemplates;
            }
        }

        /**
         * 为渠道处理订单数据
         * @param {object} orderData - 订单数据
         * @param {object} context - 上下文信息
         * @returns {object} 处理后的订单数据
         */
        processOrderForChannel(orderData, context) {
            try {
                const { detectedChannel } = context;
                
                if (!detectedChannel || !window.OTA.customizationEngine) {
                    return orderData;
                }

                // 执行渠道专属处理
                const processResult = window.OTA.customizationEngine.processOrder(
                    detectedChannel,
                    orderData
                );

                if (processResult.success) {
                    // 合并处理结果
                    const enhancedData = {
                        ...orderData,
                        ...processResult.processedData,
                        _otaChannel: detectedChannel,
                        _otaProcessing: {
                            vehicleRecommendation: processResult.vehicleRecommendation,
                            priceResult: processResult.priceResult,
                            processedAt: processResult.metadata.processedAt
                        }
                    };

                    this.logger.log('订单渠道处理完成', 'info', {
                        渠道: detectedChannel,
                        客户: enhancedData.customer_name,
                        车型推荐: processResult.vehicleRecommendation?.mappedType
                    });

                    return enhancedData;
                } else {
                    this.logger.log('订单渠道处理失败', 'error', { 
                        渠道: detectedChannel,
                        错误: processResult.error 
                    });
                    return orderData;
                }

            } catch (error) {
                this.logger.log('订单渠道处理异常', 'error', { error: error.message });
                return orderData;
            }
        }

        /**
         * 从文本中提取参考号
         * @param {string} text - 输入文本
         * @returns {string|null} 提取的参考号
         */
        extractReferenceNumber(text) {
            if (!text || typeof text !== 'string') {
                return null;
            }

            // 常见参考号模式
            const patterns = [
                /\b(CD[A-Z0-9]{6,12})\b/i,
                /\b(CT[A-Z0-9]{6,10})\b/i,
                /\b(KL[A-Z0-9]{6,10})\b/i,
                /\b(KK[A-Z0-9]{6,10})\b/i,
                /参考[号码]*[:：]\s*([A-Z0-9]+)/i,
                /reference[:：]\s*([A-Z0-9]+)/i,
                /\b([A-Z]{2,4}[0-9]{6,10})\b/
            ];

            for (const pattern of patterns) {
                const match = text.match(pattern);
                if (match) {
                    return match[1].toUpperCase();
                }
            }

            return null;
        }

        /**
         * 添加预处理钩子
         * @param {string} name - 钩子名称
         * @param {function} callback - 回调函数
         */
        addPreProcessingHook(name, callback) {
            this.integrationCallbacks.set(`pre_${name}`, callback);
        }

        /**
         * 添加提示词钩子
         * @param {string} name - 钩子名称
         * @param {function} callback - 回调函数
         */
        addPromptHook(name, callback) {
            this.integrationCallbacks.set(`prompt_${name}`, callback);
        }

        /**
         * 添加后处理钩子
         * @param {string} name - 钩子名称
         * @param {function} callback - 回调函数
         */
        addPostProcessingHook(name, callback) {
            this.integrationCallbacks.set(`post_${name}`, callback);
        }

        /**
         * 获取Gemini服务实例
         * @returns {object|null} Gemini服务实例
         */
        getGeminiService() {
            if (window.OTA && window.OTA.geminiService) {
                return window.OTA.geminiService;
            }
            if (typeof getGeminiService === 'function') {
                return getGeminiService();
            }
            return null;
        }

        /**
         * 获取嵌套属性
         * @param {object} obj - 对象
         * @param {string} path - 属性路径
         * @returns {any} 属性值
         */
        getNestedProperty(obj, path) {
            return path.split('.').reduce((current, key) => current && current[key], obj);
        }

        /**
         * 获取Logger实例
         * @returns {object} Logger实例
         */
        getLogger() {
            if (typeof getLogger === 'function') {
                return getLogger();
            }
            return {
                log: (message, level, data) => {
                    console.log(`[${level.toUpperCase()}] ${message}`, data || '');
                }
            };
        }
    }

    // 检查是否已经存在实例，避免重复创建
    if (window.OTA && window.OTA.otaGeminiIntegration) {
        console.log('🔄 OTA Gemini集成已存在，跳过重复创建');
        return;
    }

    // 创建全局实例并初始化
    const otaGeminiIntegration = new OTAGeminiIntegration();
    
    // 延迟初始化以确保其他服务已加载
    setTimeout(() => {
        otaGeminiIntegration.initialize();
    }, 100);

    // 暴露到OTA命名空间
    window.OTA.GeminiIntegration = OTAGeminiIntegration;
    window.OTA.otaGeminiIntegration = otaGeminiIntegration;

    // 注册到OTA注册中心
    if (window.OTA && window.OTA.Registry) {
        window.OTA.Registry.registerService('geminiIntegration', otaGeminiIntegration, '@OTA_GEMINI_INTEGRATION');
    }

})();