<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>减法开发示例 - 简化的OTA系统</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .status-panel {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            padding: 15px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .warning { color: #ffc107; }
        textarea {
            width: 100%;
            height: 150px;
            margin: 10px 0;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .before, .after {
            padding: 15px;
            border-radius: 4px;
        }
        .before {
            background: #ffe6e6;
            border: 1px solid #ffcccc;
        }
        .after {
            background: #e6ffe6;
            border: 1px solid #ccffcc;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>减法开发示例：简化的OTA系统</h1>
        
        <div class="comparison">
            <div class="before">
                <h3>❌ 原来的复杂系统</h3>
                <ul>
                    <li>OTAManager (原始类)</li>
                    <li>OTAManagerFactory (工厂模式)</li>
                    <li>OTAManagerDecorator (装饰器模式)</li>
                    <li>BaseManagerAdapter (适配器模式)</li>
                    <li>Mock对象系统</li>
                    <li>复杂的事件桥接</li>
                    <li>多层依赖注入</li>
                    <li>~2000行代码</li>
                </ul>
            </div>
            <div class="after">
                <h3>✅ 简化后的系统</h3>
                <ul>
                    <li>SimpleOTAManager (单一类)</li>
                    <li>直接继承BaseManager</li>
                    <li>简单的策略注册</li>
                    <li>直接调用，无事件</li>
                    <li>内联默认策略</li>
                    <li>简化的渠道检测</li>
                    <li>~200行代码 (减少90%)</li>
                </ul>
            </div>
        </div>

        <div class="status-panel">
            <h3>系统状态</h3>
            <div id="systemStatus">正在初始化...</div>
        </div>

        <div>
            <h3>测试渠道检测</h3>
            <textarea id="testInput" placeholder="输入订单文本测试渠道检测...">
订单编号：2855024653293445375
买家：ld340ren
支付时间：2025-07-30 15:12:40
飞猪订单
【接机】
马来西亚-斗湖
            </textarea>
            <br>
            <button onclick="testChannelDetection()">测试渠道检测</button>
        </div>

        <div class="status-panel">
            <h3>检测结果</h3>
            <div id="detectionResult">等待检测...</div>
        </div>

        <div class="status-panel">
            <h3>性能对比</h3>
            <div id="performanceComparison">
                <p><strong>启动时间:</strong></p>
                <p>复杂系统: ~2000ms (加载多个文件)</p>
                <p>简化系统: ~50ms (单文件)</p>
                <br>
                <p><strong>内存使用:</strong></p>
                <p>复杂系统: ~5MB (多个实例和适配器)</p>
                <p>简化系统: ~500KB (单实例)</p>
                <br>
                <p><strong>维护复杂度:</strong></p>
                <p>复杂系统: 需要理解5个抽象层</p>
                <p>简化系统: 单一责任类，易于理解</p>
            </div>
        </div>
    </div>

    <!-- 仅加载必需的脚本 -->
    <script src="js/core/dependency-container.js"></script>
    <script src="js/adapters/base-manager-adapter.js"></script>
    <script src="js/managers/simple-ota-manager.js"></script>
    <script src="js/simple-channel-detection.js"></script>

    <script>
        // 初始化简化系统
        let simpleOTA = null;

        function initializeSimpleSystem() {
            try {
                // 创建简化的OTA管理器
                simpleOTA = new SimpleOTAManager();
                simpleOTA.init();

                updateSystemStatus('✅ 简化系统初始化成功', 'success');
                
                // 显示系统状态
                const status = simpleOTA.getStatus();
                document.getElementById('systemStatus').innerHTML = `
                    <div class="success">
                        <strong>系统状态:</strong> ${status.initialized ? '已初始化' : '未初始化'}<br>
                        <strong>当前渠道:</strong> ${status.currentChannel || 'none'}<br>
                        <strong>可用策略:</strong> ${status.availableStrategies.join(', ')}<br>
                        <strong>策略数量:</strong> ${status.strategyCount}
                    </div>
                `;

            } catch (error) {
                updateSystemStatus('❌ 系统初始化失败: ' + error.message, 'error');
                console.error('System initialization failed:', error);
            }
        }

        function testChannelDetection() {
            const text = document.getElementById('testInput').value;
            
            if (!text.trim()) {
                updateDetectionResult('请输入测试文本', 'warning');
                return;
            }

            try {
                // 使用统一的渠道检测
                if (window.OTA && window.OTA.channelDetector) {
                    const result = window.OTA.channelDetector.detectChannel(text, '', { isDemo: true });

                    updateDetectionResult(`
                        <div class="success">
                            <strong>检测结果:</strong><br>
                            渠道: ${result.detectedChannel || 'default'}<br>
                            置信度: ${result.confidence}<br>
                            方法: ${result.method || 'unified-detector'}
                        </div>
                    `, 'success');

                    // 如果有OTA管理器，触发策略切换
                    if (simpleOTA) {
                        simpleOTA.handleChannelDetection(result);
                        
                        // 更新系统状态
                        const status = simpleOTA.getStatus();
                        document.getElementById('systemStatus').innerHTML = `
                            <div class="success">
                                <strong>系统状态:</strong> ${status.initialized ? '已初始化' : '未初始化'}<br>
                                <strong>当前渠道:</strong> ${status.currentChannel || 'none'}<br>
                                <strong>当前策略:</strong> ${status.currentStrategy}<br>
                                <strong>可用策略:</strong> ${status.availableStrategies.join(', ')}
                            </div>
                        `;
                    }
                } else {
                    updateDetectionResult('渠道检测器未找到', 'error');
                }

            } catch (error) {
                updateDetectionResult('检测失败: ' + error.message, 'error');
                console.error('Detection failed:', error);
            }
        }

        function updateSystemStatus(message, type) {
            const element = document.getElementById('systemStatus');
            element.innerHTML = `<div class="${type}">${message}</div>`;
        }

        function updateDetectionResult(message, type) {
            const element = document.getElementById('detectionResult');
            element.innerHTML = message;
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 启动减法开发示例');
            setTimeout(initializeSimpleSystem, 100);
        });

        // 性能监控
        window.addEventListener('load', function() {
            const loadTime = performance.now();
            document.getElementById('performanceComparison').innerHTML += `
                <br><p><strong>实际加载时间:</strong> ${Math.round(loadTime)}ms</p>
            `;
        });
    </script>
</body>
</html>
