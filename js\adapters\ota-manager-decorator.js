/**
 * OTAManager装饰器 - 零破坏性功能增强
 * 
 * 设计目标：
 * - 不修改OTAManager原有代码，通过装饰器模式增强功能
 * - 解决BaseManager继承问题
 * - 提供标准Manager接口（init方法）
 * - 保持与ApplicationBootstrap的完全兼容
 * 
 * <AUTHOR>
 * @version 1.0.0
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};
window.OTA.decorators = window.OTA.decorators || {};

(function() {
    'use strict';

    /**
     * OTAManager装饰器类
     * 为OTAManager提供标准Manager接口和BaseManager功能
     */
    class OTAManagerDecorator {
        constructor(otaManager) {
            if (!otaManager) {
                throw new Error('OTAManager instance is required');
            }

            this.otaManager = otaManager;
            this.baseAdapter = null;
            this.initialized = false;
            
            // 设置BaseManager适配器
            this.setupBaseManagerAdapter();
            
            // 代理所有原有方法和属性
            this.setupProxy();
            
            console.log('🔧 OTAManager装饰器已创建');
        }

        /**
         * 设置BaseManager适配器
         * 为OTAManager提供BaseManager功能
         */
        setupBaseManagerAdapter() {
            try {
                // 创建BaseManager适配器
                if (window.OTA.adapters && window.OTA.adapters.BaseManagerAdapter) {
                    this.baseAdapter = new window.OTA.adapters.BaseManagerAdapter('OTAManager');
                } else {
                    throw new Error('BaseManagerAdapter not available');
                }

                // 将适配器的方法注入到OTAManager中
                this.injectAdapterMethods();
                
            } catch (error) {
                console.error('Failed to setup BaseManager adapter:', error);
                // 创建降级适配器
                this.createFallbackAdapter();
            }
        }

        /**
         * 注入适配器方法到OTAManager
         */
        injectAdapterMethods() {
            const adapterMethods = ['log', 'error', 'warn', 'info', 'debug', 'emit', 'on', 'off'];
            
            adapterMethods.forEach(methodName => {
                if (this.baseAdapter[methodName]) {
                    // 强制注入所有必需方法，确保兼容性
                    if (methodName === 'log') {
                        // 特殊处理log方法
                        const originalLog = this.otaManager[methodName];
                        this.otaManager[methodName] = (...args) => {
                            try {
                                this.baseAdapter[methodName].apply(this.baseAdapter, args);
                            } catch (error) {
                                console.log('[OTAManager]', ...args);
                            }
                        };
                    } else {
                        // 直接注入其他方法
                        this.otaManager[methodName] = this.baseAdapter[methodName].bind(this.baseAdapter);
                    }
                }
            });

            // 验证注入是否成功
            const missingMethods = adapterMethods.filter(method => !this.otaManager[method]);
            if (missingMethods.length > 0) {
                console.warn('OTAManagerDecorator: 以下方法注入失败:', missingMethods);
            } else {
                console.log('OTAManagerDecorator: 所有适配器方法注入成功');
            }
        }

        /**
         * 创建降级适配器
         */
        createFallbackAdapter() {
            this.baseAdapter = {
                log: (...args) => console.log('[OTAManager]', ...args),
                error: (...args) => console.error('[OTAManager]', ...args),
                warn: (...args) => console.warn('[OTAManager]', ...args),
                info: (...args) => console.info('[OTAManager]', ...args),
                debug: (...args) => console.debug('[OTAManager]', ...args),
                emit: (eventName, data) => {
                    console.log('[OTAManager] Event emitted:', eventName, data);
                },
                on: () => {},
                off: () => {}
            };
            
            this.injectAdapterMethods();
        }

        /**
         * 设置代理，透明地转发所有方法和属性访问
         */
        setupProxy() {
            // 代理所有属性访问
            return new Proxy(this, {
                get: (target, prop) => {
                    // 优先返回装饰器自己的方法
                    if (prop in target && typeof target[prop] !== 'undefined') {
                        return target[prop];
                    }
                    
                    // 转发到原始OTAManager
                    if (prop in target.otaManager) {
                        const value = target.otaManager[prop];
                        if (typeof value === 'function') {
                            return value.bind(target.otaManager);
                        }
                        return value;
                    }
                    
                    return undefined;
                },
                
                set: (target, prop, value) => {
                    // 设置属性到原始OTAManager
                    if (prop in target.otaManager || !target.hasOwnProperty(prop)) {
                        target.otaManager[prop] = value;
                    } else {
                        target[prop] = value;
                    }
                    return true;
                }
            });
        }

        /**
         * 标准init方法 - 与ApplicationBootstrap完全兼容
         * 调用原有的initialize方法，保持功能完全一致
         */
        async init() {
            try {
                console.log('🔧 开始初始化OTAManager（通过装饰器）...');

                // 检查是否已经初始化
                if (this.initialized) {
                    console.log('⚠️ OTAManager已经初始化，跳过重复初始化');
                    return;
                }

                // 调用原有的initialize方法
                if (this.otaManager.initialize && typeof this.otaManager.initialize === 'function') {
                    await this.otaManager.initialize();
                } else {
                    throw new Error('OTAManager.initialize method not found');
                }

                this.initialized = true;

                // 使用适配器记录日志
                if (this.baseAdapter && this.baseAdapter.log) {
                    this.baseAdapter.log('OTAManager initialized successfully via decorator');
                }

                // 注册到OTA命名空间
                this.registerToOTANamespace();

                // 发出装饰器初始化完成事件
                if (this.baseAdapter && this.baseAdapter.emit) {
                    this.baseAdapter.emit('ota-manager-decorator-initialized', {
                        timestamp: Date.now(),
                        managerName: this.getName()
                    });
                }

                console.log('✅ OTAManager初始化完成（通过装饰器）');

            } catch (error) {
                console.error('❌ OTAManager初始化失败:', error);
                if (this.baseAdapter && this.baseAdapter.error) {
                    this.baseAdapter.error('Failed to initialize OTAManager', error);
                }

                // 设置初始化失败状态
                this.initialized = false;
                throw error;
            }
        }

        /**
         * 保持原有initialize方法的可用性
         * 确保向后兼容
         */
        async initialize() {
            return this.init();
        }

        /**
         * 获取原始OTAManager实例
         * @returns {Object} 原始OTAManager实例
         */
        getOriginalManager() {
            return this.otaManager;
        }

        /**
         * 获取BaseManager适配器
         * @returns {Object} BaseManager适配器实例
         */
        getBaseAdapter() {
            return this.baseAdapter;
        }

        /**
         * 检查初始化状态
         * @returns {boolean} 初始化状态
         */
        isInitialized() {
            return this.initialized && (this.otaManager.initialized || false);
        }

        /**
         * 获取管理器名称
         * @returns {string} 管理器名称
         */
        getName() {
            return 'OTAManager';
        }

        /**
         * 注册到OTA命名空间
         * 确保装饰后的OTAManager可以通过标准方式访问
         */
        registerToOTANamespace() {
            try {
                // 注册到OTA命名空间
                if (window.OTA) {
                    window.OTA.otaManager = this;

                    // 如果有注册中心，也注册到注册中心
                    if (window.OTA.Registry && typeof window.OTA.Registry.registerService === 'function') {
                        window.OTA.Registry.registerService('otaManager', this, '@OTA_MANAGER');
                    }

                    console.log('✅ OTAManager已注册到OTA命名空间');
                }
            } catch (error) {
                console.warn('⚠️ 注册到OTA命名空间失败:', error);
            }
        }

        /**
         * 获取管理器统计信息
         * @returns {Object} 统计信息
         */
        getStats() {
            const stats = {
                name: this.getName(),
                initialized: this.isInitialized(),
                hasBaseAdapter: !!this.baseAdapter,
                hasOriginalManager: !!this.otaManager,
                strategyCount: 0,
                currentChannel: null,
                timestamp: Date.now()
            };

            // 获取策略相关统计
            if (this.otaManager) {
                if (this.otaManager.strategyRegistry) {
                    stats.strategyCount = this.otaManager.strategyRegistry.size;
                }
                if (this.otaManager.currentChannel) {
                    stats.currentChannel = this.otaManager.currentChannel;
                }
            }

            return stats;
        }

        /**
         * 健康检查
         * @returns {Object} 健康状态
         */
        healthCheck() {
            const health = {
                healthy: true,
                issues: [],
                timestamp: Date.now()
            };

            // 检查基础组件
            if (!this.baseAdapter) {
                health.healthy = false;
                health.issues.push('BaseAdapter not available');
            }

            if (!this.otaManager) {
                health.healthy = false;
                health.issues.push('Original OTAManager not available');
            }

            // 检查初始化状态
            if (!this.initialized) {
                health.healthy = false;
                health.issues.push('Manager not initialized');
            }

            // 检查关键方法
            const requiredMethods = ['log', 'error', 'emit', 'initialize'];
            requiredMethods.forEach(method => {
                if (!this.otaManager || typeof this.otaManager[method] !== 'function') {
                    health.issues.push(`Missing method: ${method}`);
                }
            });

            if (health.issues.length > 0) {
                health.healthy = false;
            }

            return health;
        }

        /**
         * 销毁装饰器
         */
        destroy() {
            try {
                console.log('🔧 开始销毁OTAManager装饰器...');

                // 发出销毁事件
                if (this.baseAdapter && this.baseAdapter.emit) {
                    this.baseAdapter.emit('ota-manager-decorator-destroying', {
                        timestamp: Date.now()
                    });
                }

                // 销毁原始管理器
                if (this.otaManager && typeof this.otaManager.destroy === 'function') {
                    this.otaManager.destroy();
                }

                // 销毁适配器
                if (this.baseAdapter && this.baseAdapter.destroy) {
                    this.baseAdapter.destroy();
                }

                // 从OTA命名空间移除
                if (window.OTA && window.OTA.otaManager === this) {
                    delete window.OTA.otaManager;
                }

                // 重置状态
                this.initialized = false;
                this.baseAdapter = null;
                this.otaManager = null;

                console.log('✅ OTAManager装饰器已销毁');

            } catch (error) {
                console.error('❌ 销毁OTAManager装饰器时出错:', error);
            }
        }
    }

    // 暴露到OTA命名空间
    window.OTA.decorators.OTAManagerDecorator = OTAManagerDecorator;

    // 提供便捷的创建函数
    window.OTA.createOTAManagerDecorator = function(otaManager) {
        return new OTAManagerDecorator(otaManager);
    };

    console.log('✅ OTAManager装饰器已加载');

})();
