/**
 * @OTA_CORE OTA全局变量注册中心
 * 🏷️ 标签: @OTA_REGISTRY_CORE
 * 📝 功能: 统一管理所有全局变量和服务
 * ⚠️ 警告: 已注册，请勿重复开发
 * 
 * 该模块解决过度开发中发现的问题:
 * - 100+全局变量污染
 * - 38个文件中重复定义getLogger
 * - 4个位置重复定义getAppState
 * - 3个位置重复定义getGeminiService
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};

(function() {
    'use strict';

    /**
     * @OTA_CORE 统一变量注册中心
     * 防止重复开发和全局变量污染
     */
    const OTARegistry = {
        // 注册表
        services: new Map(),
        managers: new Map(), 
        factories: new Map(),
        utils: new Map(),
        
        // 重复检测计数器
        duplicateDetections: new Map(),
        
        /**
         * 注册服务 - 自动检查重复
         * @param {string} name 服务名称
         * @param {*} service 服务实例或类
         * @param {string} tag 标签标识
         */
        registerService(name, service, tag) {
            if (this.services.has(name)) {
                const existing = this.services.get(name);
                console.warn(`🚨 重复服务注册检测: ${name}`, {
                    newTag: tag,
                    existingTag: existing.tag,
                    newRegisteredAt: new Date(),
                    existingRegisteredAt: existing.registeredAt
                });
                
                // 统计重复次数
                const count = this.duplicateDetections.get(name) || 0;
                this.duplicateDetections.set(name, count + 1);
            }
            
            this.services.set(name, { 
                service, 
                tag, 
                registeredAt: new Date(),
                type: 'service'
            });
            
            this.logRegistration('SERVICE', name, tag);
        },
        
        /**
         * 注册管理器
         */
        registerManager(name, manager, tag) {
            if (this.managers.has(name)) {
                console.warn(`🚨 重复管理器注册: ${name} (标签: ${tag})`);
            }
            this.managers.set(name, { manager, tag, registeredAt: new Date() });
            this.logRegistration('MANAGER', name, tag);
        },
        
        /**
         * 注册工厂函数
         */
        registerFactory(name, factory, tag) {
            if (this.factories.has(name)) {
                console.warn(`🚨 重复工厂函数注册: ${name} (标签: ${tag})`);
            }
            this.factories.set(name, { factory, tag, registeredAt: new Date() });
            this.logRegistration('FACTORY', name, tag);
        },
        
        /**
         * 注册工具函数
         */
        registerUtil(name, util, tag) {
            if (this.utils.has(name)) {
                console.warn(`🚨 重复工具函数注册: ${name} (标签: ${tag})`);
            }
            this.utils.set(name, { util, tag, registeredAt: new Date() });
            this.logRegistration('UTIL', name, tag);
        },
        
        /**
         * 获取注册信息摘要
         */
        getRegistryInfo() {
            return {
                services: Array.from(this.services.keys()),
                managers: Array.from(this.managers.keys()),
                factories: Array.from(this.factories.keys()),
                utils: Array.from(this.utils.keys()),
                totalRegistered: this.services.size + this.managers.size + this.factories.size + this.utils.size,
                duplicateDetections: Array.from(this.duplicateDetections.entries()),
                totalDuplicates: Array.from(this.duplicateDetections.values()).reduce((a, b) => a + b, 0)
            };
        },
        
        /**
         * 获取指定类型的注册项
         */
        getRegistryByType(type) {
            switch(type.toLowerCase()) {
                case 'service': return this.services;
                case 'manager': return this.managers;
                case 'factory': return this.factories;
                case 'util': return this.utils;
                default: return null;
            }
        },
        
        /**
         * 检查是否已注册
         */
        isRegistered(name, type = 'service') {
            const registry = this.getRegistryByType(type);
            return registry ? registry.has(name) : false;
        },
        
        /**
         * 生成注册报告
         */
        generateReport() {
            const info = this.getRegistryInfo();
            const report = {
                timestamp: new Date().toISOString(),
                summary: {
                    totalRegistered: info.totalRegistered,
                    totalDuplicates: info.totalDuplicates,
                    duplicateRate: (info.totalDuplicates / info.totalRegistered * 100).toFixed(2) + '%'
                },
                breakdown: {
                    services: info.services.length,
                    managers: info.managers.length,
                    factories: info.factories.length,
                    utils: info.utils.length
                },
                duplicateIssues: info.duplicateDetections,
                registeredServices: info.services,
                registeredManagers: info.managers,
                registeredFactories: info.factories,
                registeredUtils: info.utils
            };
            
            return report;
        },
        
        /**
         * 私有方法：记录注册日志
         */
        logRegistration(type, name, tag) {
            const logger = window.getLogger ? window.getLogger() : console;
            if (logger && logger.log) {
                logger.log(`📋 OTA注册: ${type}`, 'info', {
                    name,
                    tag,
                    timestamp: new Date()
                });
            }
        }
    };

    // 注册到OTA命名空间
    window.OTA.Registry = OTARegistry;
    
    // 全局访问（向后兼容）
    window.OTARegistry = OTARegistry;
    
    // 初始化日志
    const logger = window.getLogger ? window.getLogger() : console;
    if (logger && logger.log) {
        logger.log('🚀 OTA注册中心已初始化', 'info', {
            version: '1.0.0',
            features: ['重复检测', '标签管理', '注册报告']
        });
    }

    // 导出调试命令到全局
    window.otaRegistryReport = () => {
        const report = OTARegistry.generateReport();
        console.table(report.breakdown);
        console.log('📊 完整注册报告:', report);
        return report;
    };

})();