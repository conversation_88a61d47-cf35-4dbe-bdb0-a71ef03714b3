/**
 * @OTA_MULTI_ORDER_INTEGRATION OTA系统与多订单管理器集成
 * 🏷️ 标签: @OTA_MULTI_ORDER_INTEGRATION
 * 📝 说明: 将OTA渠道识别和定制化功能集成到多订单管理系统中
 * <AUTHOR>
 * @version 1.0.0
 */

(function() {
    'use strict';

    // 确保OTA命名空间存在
    window.OTA = window.OTA || {};

    /**
     * OTA与多订单管理器集成
     */
    class OTAMultiOrderIntegration {
        constructor() {
            this.logger = this.getLogger();
            this.isInitialized = false;
            this.channelCache = new Map();
            this.processingQueue = [];
        }

        /**
         * 初始化集成
         * @returns {boolean} 初始化是否成功
         */
        initialize() {
            try {
                // 检查依赖服务是否可用
                if (!this.checkDependencies()) {
                    throw new Error('Required services not available');
                }

                // 扩展多订单管理器
                this.extendMultiOrderManager();
                
                // 注册事件监听器
                this.registerEventListeners();

                this.isInitialized = true;
                this.logger.log('OTA-MultiOrder集成初始化成功', 'info');
                return true;

            } catch (error) {
                this.logger.log('OTA-MultiOrder集成初始化失败', 'error', { error: error.message });
                return false;
            }
        }

        /**
         * 检查依赖服务
         * @returns {boolean} 依赖是否满足
         */
        checkDependencies() {
            const required = [
                'window.OTA.channelDetector',
                'window.OTA.customizationEngine'
            ];

            for (const dependency of required) {
                if (!this.getNestedProperty(window, dependency)) {
                    this.logger.log(`缺少依赖: ${dependency}`, 'error');
                    return false;
                }
            }

            return true;
        }

        /**
         * 扩展多订单管理器
         */
        extendMultiOrderManager() {
            // 获取多订单管理器
            const multiOrderManager = this.getMultiOrderManager();
            if (!multiOrderManager) {
                throw new Error('MultiOrderManager not found');
            }

            // 保存原始方法
            const originalParseMultiOrderText = multiOrderManager.parseMultiOrderText;
            const originalRenderOrderCards = multiOrderManager.renderOrderCards;
            const originalProcessBatchOrders = multiOrderManager.processBatchOrders;

            // 扩展多订单解析方法
            multiOrderManager.parseMultiOrderText = async function(text) {
                try {
                    // 执行全局渠道检测
                    const globalDetection = window.OTA.otaMultiOrderIntegration.performGlobalChannelDetection(text);
                    
                    // 调用原始解析方法
                    const result = await originalParseMultiOrderText.call(this, text);
                    
                    // 增强解析结果
                    if (result && result.orders) {
                        result.orders = await window.OTA.otaMultiOrderIntegration.enhanceMultiOrderData(
                            result.orders, 
                            globalDetection,
                            text
                        );
                        
                        // 添加全局渠道信息
                        result.globalChannel = globalDetection.detectedChannel;
                        result.channelConfidence = globalDetection.confidence;
                    }
                    
                    return result;
                    
                } catch (error) {
                    window.OTA.otaMultiOrderIntegration.logger.log('OTA增强多订单解析失败，回退到原始方法', 'warning', { error: error.message });
                    return await originalParseMultiOrderText.call(this, text);
                }
            };

            // 扩展订单卡片渲染方法
            multiOrderManager.renderOrderCards = function(orders, container) {
                try {
                    // 调用原始渲染方法
                    const result = originalRenderOrderCards.call(this, orders, container);
                    
                    // 添加渠道特定的UI增强
                    window.OTA.otaMultiOrderIntegration.enhanceOrderCardsUI(orders, container);
                    
                    return result;
                    
                } catch (error) {
                    window.OTA.otaMultiOrderIntegration.logger.log('订单卡片UI增强失败', 'warning', { error: error.message });
                    return originalRenderOrderCards.call(this, orders, container);
                }
            };

            // 扩展批量处理方法
            multiOrderManager.processBatchOrders = async function(orders) {
                try {
                    // 预处理：按渠道分组
                    const groupedOrders = window.OTA.otaMultiOrderIntegration.groupOrdersByChannel(orders);
                    
                    // 为每个渠道组应用专属处理
                    const enhancedOrders = await window.OTA.otaMultiOrderIntegration.processBatchByChannel(groupedOrders);
                    
                    // 调用原始批量处理方法
                    return await originalProcessBatchOrders.call(this, enhancedOrders);
                    
                } catch (error) {
                    window.OTA.otaMultiOrderIntegration.logger.log('OTA增强批量处理失败，回退到原始方法', 'warning', { error: error.message });
                    return await originalProcessBatchOrders.call(this, orders);
                }
            };

            this.logger.log('多订单管理器扩展完成', 'info');
        }

        /**
         * 执行简化的渠道检测
         * @param {string} text - 输入文本
         * @returns {object} 检测结果
         */
        performGlobalChannelDetection(text) {
            try {
                // 简化：直接执行检测，不使用复杂的缓存机制
                const detectionResult = window.OTA.channelDetector.detectChannel(
                    text,
                    '', // 空参考号
                    { isMultiOrder: true }
                );

                this.logger.log('渠道检测完成', 'info', {
                    检测到的渠道: detectionResult.detectedChannel,
                    置信度: detectionResult.confidence
                });

                return detectionResult;

            } catch (error) {
                this.logger.log('渠道检测失败', 'error', { error: error.message });
                return {
                    detectedChannel: null,
                    confidence: 0,
                    error: error.message
                };
            }
        }

        /**
         * 简化的订单数据处理 - 删除复杂的JavaScript增强逻辑
         * @param {array} orders - 订单列表
         * @param {object} globalDetection - 全局检测结果
         * @param {string} _originalText - 原始文本（未使用）
         * @returns {array} 订单列表（保持原样，让Gemini处理所有逻辑）
         */
        async enhanceMultiOrderData(orders, globalDetection, _originalText) {
            // 简化：不再进行复杂的JavaScript处理，直接返回Gemini解析的结果
            // 渠道特定的处理逻辑已经通过提示词片段在Gemini中完成
            return orders.map((order, index) => ({
                ...order,
                _otaChannel: globalDetection.detectedChannel,
                _otaConfidence: globalDetection.confidence,
                _orderIndex: index
            }));
        }





        /**
         * 按渠道分组订单
         * @param {array} orders - 订单列表
         * @returns {object} 按渠道分组的订单
         */
        groupOrdersByChannel(orders) {
            const groups = {};

            orders.forEach(order => {
                const channel = order._otaChannel || 'unknown';
                if (!groups[channel]) {
                    groups[channel] = [];
                }
                groups[channel].push(order);
            });

            this.logger.log('订单分组完成', 'info', {
                渠道数量: Object.keys(groups).length,
                分组详情: Object.keys(groups).map(channel => ({
                    渠道: channel,
                    订单数: groups[channel].length
                }))
            });

            return groups;
        }

        /**
         * 按渠道分组处理批量订单
         * @param {object} groupedOrders - 分组后的订单
         * @returns {array} 处理后的订单列表
         */
        async processBatchByChannel(groupedOrders) {
            const allProcessedOrders = [];

            for (const [channel, orders] of Object.entries(groupedOrders)) {
                try {
                    this.logger.log(`开始处理${channel}渠道的${orders.length}个订单`, 'info');
                    
                    // 应用渠道特定的批量处理逻辑
                    const processedOrders = await this.processChannelBatch(channel, orders);
                    allProcessedOrders.push(...processedOrders);
                    
                } catch (error) {
                    this.logger.log(`${channel}渠道批量处理失败`, 'error', { error: error.message });
                    // 添加错误标记但保留订单
                    const errorOrders = orders.map(order => ({
                        ...order,
                        _batchProcessingError: error.message
                    }));
                    allProcessedOrders.push(...errorOrders);
                }
            }

            return allProcessedOrders;
        }

        /**
         * 处理特定渠道的批量订单
         * @param {string} channel - 渠道名称
         * @param {array} orders - 订单列表
         * @returns {array} 处理后的订单列表
         */
        async processChannelBatch(channel, orders) {
            // 渠道特定的批量优化
            const channelConfig = window.OTA.customizationEngine.getChannelConfig(channel);
            
            // 统一语言设置
            const channelLanguages = window.OTA.getChannelLanguages ? window.OTA.getChannelLanguages(channel) : [2, 4];
            orders.forEach(order => {
                if (!order.languages_id_array || order.languages_id_array.length === 0) {
                    order.languages_id_array = channelLanguages;
                }
            });

            return orders;
        }

        /**
         * 增强订单卡片UI
         * @param {array} orders - 订单列表
         * @param {HTMLElement} container - 容器元素
         */
        enhanceOrderCardsUI(orders, container) {
            try {
                orders.forEach((order, index) => {
                    const cardElement = container.children[index];
                    if (!cardElement) return;

                    // 添加渠道标识
                    if (order._otaChannel) {
                        this.addChannelBadge(cardElement, order._otaChannel, order._otaConfidence);
                    }

                    // 添加车型推荐信息
                    if (order._otaProcessing && order._otaProcessing.vehicleRecommendation) {
                        this.addVehicleRecommendation(cardElement, order._otaProcessing.vehicleRecommendation);
                    }

                    // 添加价格信息
                    if (order._otaProcessing && order._otaProcessing.priceResult) {
                        this.addPriceInformation(cardElement, order._otaProcessing.priceResult);
                    }
                });

            } catch (error) {
                this.logger.log('UI增强失败', 'error', { error: error.message });
            }
        }

        /**
         * 添加渠道标识
         * @param {HTMLElement} cardElement - 卡片元素
         * @param {string} channel - 渠道名称
         * @param {number} confidence - 置信度
         */
        addChannelBadge(cardElement, channel, confidence) {
            const badge = document.createElement('div');
            badge.className = 'ota-channel-badge';
            badge.style.cssText = `
                position: absolute;
                top: 10px;
                right: 10px;
                background: ${this.getChannelColor(channel)};
                color: white;
                padding: 2px 8px;
                border-radius: 4px;
                font-size: 12px;
                font-weight: bold;
                z-index: 10;
            `;
            badge.textContent = channel;
            badge.title = `渠道: ${channel} (置信度: ${Math.round(confidence * 100)}%)`;
            
            cardElement.style.position = 'relative';
            cardElement.appendChild(badge);
        }

        /**
         * 获取渠道颜色
         * @param {string} channel - 渠道名称
         * @returns {string} 颜色值
         */
        getChannelColor(channel) {
            const colors = {
                'Chong Dealer': '#FF6B6B',
                'Ctrip': '#4ECDC4',
                'Klook West Malaysia': '#45B7D1',
                'Kkday': '#96CEB4',
                'default': '#95A5A6'
            };
            return colors[channel] || colors['default'];
        }

        /**
         * 从订单文本中提取参考号
         * @param {string} orderText - 订单文本
         * @returns {string|null} 参考号
         */
        extractReferenceFromOrder(orderText) {
            // 实现参考号提取逻辑，类似于gemini-integration.js中的方法
            if (!orderText || typeof orderText !== 'string') {
                return null;
            }

            const patterns = [
                /\b(CD[A-Z0-9]{6,12})\b/i,
                /团号[:：]\s*([A-Z0-9]+)/i,
                /参考号[:：]\s*([A-Z0-9]+)/i
            ];

            for (const pattern of patterns) {
                const match = orderText.match(pattern);
                if (match) {
                    return match[1].toUpperCase();
                }
            }

            return null;
        }

        /**
         * 生成缓存键
         * @param {string} text - 文本
         * @returns {string} 缓存键
         */
        generateCacheKey(text) {
            // 简单的哈希函数
            let hash = 0;
            for (let i = 0; i < Math.min(text.length, 1000); i++) {
                const char = text.charCodeAt(i);
                hash = ((hash << 5) - hash) + char;
                hash = hash & hash; // 转换为32位整数
            }
            return `channel_${Math.abs(hash)}`;
        }

        /**
         * 注册事件监听器
         */
        registerEventListeners() {
            // 监听多订单模式变化
            if (window.addEventListener) {
                window.addEventListener('multiOrderModeChanged', (event) => {
                    this.logger.log('多订单模式变化', 'info', { 
                        enabled: event.detail.enabled 
                    });
                });
            }
        }

        /**
         * 获取多订单管理器实例
         * @returns {object|null} 多订单管理器实例
         */
        getMultiOrderManager() {
            if (window.OTA && window.OTA.multiOrderManager) {
                return window.OTA.multiOrderManager;
            }
            if (typeof getMultiOrderManager === 'function') {
                return getMultiOrderManager();
            }
            return null;
        }

        /**
         * 获取嵌套属性
         * @param {object} obj - 对象
         * @param {string} path - 属性路径
         * @returns {any} 属性值
         */
        getNestedProperty(obj, path) {
            return path.split('.').reduce((current, key) => current && current[key], obj);
        }

        /**
         * 获取Logger实例
         * @returns {object} Logger实例
         */
        getLogger() {
            if (typeof getLogger === 'function') {
                return getLogger();
            }
            return {
                log: (message, level, data) => {
                    console.log(`[${level.toUpperCase()}] ${message}`, data || '');
                }
            };
        }
    }

    // 检查是否已经存在实例，避免重复创建
    if (window.OTA && window.OTA.otaMultiOrderIntegration) {
        console.log('🔄 OTA多订单集成已存在，跳过重复创建');
        return;
    }

    // 创建全局实例并初始化
    const otaMultiOrderIntegration = new OTAMultiOrderIntegration();
    
    // 延迟初始化以确保其他服务已加载
    setTimeout(() => {
        otaMultiOrderIntegration.initialize();
    }, 200);

    // 暴露到OTA命名空间
    window.OTA.MultiOrderIntegration = OTAMultiOrderIntegration;
    window.OTA.otaMultiOrderIntegration = otaMultiOrderIntegration;

    // 注册到OTA注册中心
    if (window.OTA && window.OTA.Registry) {
        window.OTA.Registry.registerService('multiOrderIntegration', otaMultiOrderIntegration, '@OTA_MULTI_ORDER_INTEGRATION');
    }

})();