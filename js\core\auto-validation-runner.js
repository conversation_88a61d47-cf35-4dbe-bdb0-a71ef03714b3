/**
 * 自动验证运行器
 * 
 * 设计目标：
 * - 在系统启动后自动执行接口兼容性验证
 * - 监控OTAManager的集成状态
 * - 提供实时的验证结果报告
 * - 支持持续监控和健康检查
 * 
 * <AUTHOR>
 * @version 1.0.0
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};
window.OTA.validation = window.OTA.validation || {};

(function() {
    'use strict';

    /**
     * 自动验证运行器
     */
    class AutoValidationRunner {
        constructor() {
            this.logger = this.getLogger();
            this.validationHistory = [];
            this.isRunning = false;
            this.intervalId = null;
            this.config = {
                autoStart: false,
                initialDelay: 3000,      // 3秒后开始首次验证
                intervalDelay: 30000,    // 30秒间隔进行持续监控
                maxHistorySize: 50       // 最多保存50次验证历史
            };
            
            this.logger.log('自动验证运行器已初始化');
            
            // 受特性开关控制
            try {
                const enabled = window.OTA?.featureToggle?.isEnabled('enableInterfaceValidation');
                if (enabled) {
                    this.config.autoStart = true;
                    this.scheduleInitialValidation();
                } else {
                    this.logger.log('自动验证运行器已加载（未启用，受特性开关控制）');
                }
            } catch (_) { /* 忽略 */ }
        }

        /**
         * 安排初始验证
         */
        scheduleInitialValidation() {
            setTimeout(async () => {
                try {
                    await this.runInitialValidation();
                } catch (error) {
                    this.logger.log('初始验证失败', 'error', error);
                }
            }, this.config.initialDelay);
        }

        /**
         * 运行初始验证
         */
        async runInitialValidation() {
            this.logger.log('开始运行初始验证...');
            
            try {
                // 等待系统完全加载
                await this.waitForSystemReady();
                
                // 执行完整验证
                const validationResult = await this.runFullValidation();
                
                // 记录验证结果
                this.recordValidationResult(validationResult, 'initial');
                
                // 报告验证结果
                this.reportValidationResult(validationResult, 'initial');
                
                // 如果初始验证通过，启动持续监控
                if (validationResult.overall) {
                    this.startContinuousMonitoring();
                } else {
                    this.logger.log('初始验证失败，不启动持续监控', 'warn');
                    this.handleValidationFailure(validationResult);
                }
                
            } catch (error) {
                this.logger.log('运行初始验证时出错', 'error', error);
            }
        }

        /**
         * 等待系统准备就绪
         * @returns {Promise<boolean>} 系统是否就绪
         */
        async waitForSystemReady() {
            const maxWaitTime = 10000; // 最多等待10秒
            const checkInterval = 500;  // 每500ms检查一次
            const startTime = Date.now();
            
            while (Date.now() - startTime < maxWaitTime) {
                if (this.isSystemReady()) {
                    this.logger.log('系统已准备就绪');
                    return true;
                }
                
                await this.sleep(checkInterval);
            }
            
            this.logger.log('等待系统就绪超时', 'warn');
            return false;
        }

        /**
         * 检查系统是否准备就绪
         * @returns {boolean} 系统是否就绪
         */
        isSystemReady() {
            const requiredComponents = [
                'window.OTA',
                'window.OTA.container'
            ];
            
            return requiredComponents.every(component => {
                const parts = component.split('.');
                let obj = window;
                
                for (const part of parts) {
                    if (!obj || !obj[part]) {
                        return false;
                    }
                    obj = obj[part];
                }
                
                return true;
            });
        }

        /**
         * 运行完整验证
         * @returns {Promise<Object>} 验证结果
         */
        async runFullValidation() {
            const validationResult = {
                timestamp: Date.now(),
                type: 'full',
                overall: true,
                components: {},
                issues: [],
                recommendations: []
            };

            try {
                // 1. 验证OTA Bootstrap集成状态
                const bootstrapStatus = this.validateBootstrapIntegration();
                validationResult.components.bootstrap = bootstrapStatus;
                if (!bootstrapStatus.passed) {
                    validationResult.overall = false;
                    validationResult.issues.push(...bootstrapStatus.issues);
                }

                // 2. 验证接口兼容性
                if (window.OTA.interfaceValidator && window.OTA?.featureToggle?.isEnabled('enableInterfaceValidation')) {
                    const interfaceResult = await window.OTA.interfaceValidator.runFullValidation();
                    validationResult.components.interfaces = interfaceResult;
                    if (!interfaceResult.overall) {
                        validationResult.overall = false;
                        // 从接口验证结果中提取问题
                        Object.values(interfaceResult.managers).forEach(managerResult => {
                            if (managerResult.issues) {
                                validationResult.issues.push(...managerResult.issues);
                            }
                            if (managerResult.recommendations) {
                                validationResult.recommendations.push(...managerResult.recommendations);
                            }
                        });
                    }
                }

                // 3. 验证功能连续性保障机制
                const continuityStatus = this.validateContinuityMechanisms();
                validationResult.components.continuity = continuityStatus;
                if (!continuityStatus.passed) {
                    validationResult.overall = false;
                    validationResult.issues.push(...continuityStatus.issues);
                }

                return validationResult;

            } catch (error) {
                this.logger.log('运行完整验证时出错', 'error', error);
                validationResult.overall = false;
                validationResult.error = error.message;
                return validationResult;
            }
        }

        /**
         * 验证Bootstrap集成状态
         * @returns {Object} 验证结果
         */
        validateBootstrapIntegration() {
            const result = {
                passed: true,
                issues: [],
                details: {}
            };

            try {
                // 检查集成器状态
                if (window.OTA.otaBootstrapIntegration) {
                    const integrationStatus = window.OTA.otaBootstrapIntegration.getIntegrationStatus();
                    result.details.integrationStatus = integrationStatus;
                    
                    if (!integrationStatus.integrated) {
                        result.passed = false;
                        result.issues.push('OTAManager未成功集成到Bootstrap');
                    }
                    
                    if (!integrationStatus.hasOTAManager) {
                        result.passed = false;
                        result.issues.push('OTAManager实例不存在');
                    }
                } else {
                    result.passed = false;
                    result.issues.push('Bootstrap集成器不存在');
                }

            } catch (error) {
                result.passed = false;
                result.issues.push(`Bootstrap集成验证出错: ${error.message}`);
            }

            return result;
        }

        /**
         * 验证功能连续性保障机制
         * @returns {Object} 验证结果
         */
        validateContinuityMechanisms() {
            const result = {
                passed: true,
                issues: [],
                details: {}
            };

            try {
                // 检查特性开关
                if (window.OTA.featureToggle) {
                    result.details.featureToggle = {
                        available: true,
                        features: Object.keys(window.OTA.featureToggle.getAllFeatures()).length
                    };
                } else {
                    result.passed = false;
                    result.issues.push('特性开关机制不可用');
                }

                // 检查影子部署
                if (window.OTA.shadowDeployment && window.OTA?.featureToggle?.isEnabled('enableShadowDeployment')) {
                    result.details.shadowDeployment = {
                        available: true
                    };
                }

                // 检查热回滚
                if (window.OTA.hotRollback && (window.OTA?.featureToggle?.isEnabled('enableEmergencyRollback') || window.OTA?.featureToggle?.isEnabled('enableSafeMode'))) {
                    result.details.hotRollback = {
                        available: true,
                        snapshots: window.OTA.hotRollback.getSnapshots().length
                    };
                }

            } catch (error) {
                result.passed = false;
                result.issues.push(`功能连续性验证出错: ${error.message}`);
            }

            return result;
        }

        /**
         * 记录验证结果
         * @param {Object} result - 验证结果
         * @param {string} type - 验证类型
         */
        recordValidationResult(result, type) {
            const record = {
                ...result,
                type,
                id: `validation_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
            };

            this.validationHistory.push(record);

            // 限制历史记录大小
            if (this.validationHistory.length > this.config.maxHistorySize) {
                this.validationHistory = this.validationHistory.slice(-this.config.maxHistorySize);
            }
        }

        /**
         * 报告验证结果
         * @param {Object} result - 验证结果
         * @param {string} type - 验证类型
         */
        reportValidationResult(result, type) {
            const status = result.overall ? '✅ 通过' : '❌ 失败';
            this.logger.log(`${type}验证结果: ${status}`);

            if (result.issues && result.issues.length > 0) {
                this.logger.log(`发现 ${result.issues.length} 个问题:`, 'warn');
                result.issues.forEach((issue, index) => {
                    this.logger.log(`  ${index + 1}. ${issue}`, 'warn');
                });
            }

            if (result.recommendations && result.recommendations.length > 0) {
                this.logger.log(`建议 ${result.recommendations.length} 项改进:`, 'info');
                result.recommendations.forEach((rec, index) => {
                    this.logger.log(`  ${index + 1}. ${rec}`, 'info');
                });
            }

            // 发出验证完成事件
            if (window.OTA.eventCoordinator) {
                window.OTA.eventCoordinator.emit('validation-completed', {
                    type,
                    result,
                    timestamp: Date.now()
                });
            }
        }

        /**
         * 处理验证失败
         * @param {Object} result - 验证结果
         */
        handleValidationFailure(result) {
            this.logger.log('处理验证失败...', 'warn');

            // 如果有热回滚机制，考虑创建安全快照
            if (window.OTA.hotRollback) {
                try {
                    window.OTA.hotRollback.createSnapshot('validation-failed', '验证失败时的系统状态');
                    this.logger.log('已创建验证失败时的系统快照');
                } catch (error) {
                    this.logger.log('创建安全快照失败', 'error', error);
                }
            }

            // 发出验证失败事件
            if (window.OTA.eventCoordinator) {
                window.OTA.eventCoordinator.emit('validation-failed', {
                    result,
                    timestamp: Date.now()
                });
            }
        }

        /**
         * 启动持续监控
         */
        startContinuousMonitoring() {
            if (this.isRunning) {
                this.logger.log('持续监控已在运行', 'warn');
                return;
            }

            this.isRunning = true;
            this.intervalId = setInterval(async () => {
                try {
                    const result = await this.runHealthCheck();
                    this.recordValidationResult(result, 'monitoring');
                    
                    if (!result.overall) {
                        this.logger.log('健康检查发现问题', 'warn');
                        this.reportValidationResult(result, 'monitoring');
                    }
                } catch (error) {
                    this.logger.log('持续监控出错', 'error', error);
                }
            }, this.config.intervalDelay);

            this.logger.log('持续监控已启动');
        }

        /**
         * 停止持续监控
         */
        stopContinuousMonitoring() {
            if (this.intervalId) {
                clearInterval(this.intervalId);
                this.intervalId = null;
            }
            this.isRunning = false;
            this.logger.log('持续监控已停止');
        }

        /**
         * 运行健康检查
         * @returns {Promise<Object>} 健康检查结果
         */
        async runHealthCheck() {
            const healthResult = {
                timestamp: Date.now(),
                type: 'health-check',
                overall: true,
                checks: {},
                issues: []
            };

            try {
                // 检查OTAManager健康状态
                if (window.OTA.otaManager && typeof window.OTA.otaManager.healthCheck === 'function') {
                    const otaHealth = window.OTA.otaManager.healthCheck();
                    healthResult.checks.otaManager = otaHealth;
                    if (!otaHealth.healthy) {
                        healthResult.overall = false;
                        healthResult.issues.push(...otaHealth.issues);
                    }
                }

                // 检查系统基础组件
                const systemHealth = this.checkSystemHealth();
                healthResult.checks.system = systemHealth;
                if (!systemHealth.healthy) {
                    healthResult.overall = false;
                    healthResult.issues.push(...systemHealth.issues);
                }

            } catch (error) {
                healthResult.overall = false;
                healthResult.issues.push(`健康检查出错: ${error.message}`);
            }

            return healthResult;
        }

        /**
         * 检查系统健康状态
         * @returns {Object} 系统健康状态
         */
        checkSystemHealth() {
            const health = {
                healthy: true,
                issues: []
            };

            // 检查关键组件是否存在
            const criticalComponents = [
                'window.OTA',
                'window.OTA.container',
                'window.OTA.otaManager'
            ];

            criticalComponents.forEach(component => {
                if (!this.checkComponentExists(component)) {
                    health.healthy = false;
                    health.issues.push(`关键组件缺失: ${component}`);
                }
            });

            return health;
        }

        /**
         * 检查组件是否存在
         * @param {string} componentPath - 组件路径
         * @returns {boolean} 组件是否存在
         */
        checkComponentExists(componentPath) {
            const parts = componentPath.split('.');
            let obj = window;
            
            for (const part of parts) {
                if (!obj || !obj[part]) {
                    return false;
                }
                obj = obj[part];
            }
            
            return true;
        }

        /**
         * 获取验证历史
         * @returns {Array} 验证历史记录
         */
        getValidationHistory() {
            return [...this.validationHistory];
        }

        /**
         * 获取最新验证结果
         * @returns {Object|null} 最新验证结果
         */
        getLatestValidationResult() {
            return this.validationHistory.length > 0 
                ? this.validationHistory[this.validationHistory.length - 1] 
                : null;
        }

        /**
         * 睡眠函数
         * @param {number} ms - 毫秒数
         * @returns {Promise<void>}
         */
        sleep(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        }

        /**
         * 获取Logger实例
         * @returns {Object} Logger实例
         */
        getLogger() {
            try {
                if (typeof getLogger === 'function') {
                    return getLogger();
                }
                return {
                    log: (message, level = 'info', data = null) => {
                        console.log(`[AutoValidation][${level.toUpperCase()}] ${message}`, data || '');
                    }
                };
            } catch (error) {
                return {
                    log: (message, level = 'info', data = null) => {
                        console.log(`[AutoValidation][${level.toUpperCase()}] ${message}`, data || '');
                    }
                };
            }
        }
    }

    // 创建全局自动验证运行器实例
    const autoValidationRunner = new AutoValidationRunner();

    // 暴露到OTA命名空间
    window.OTA.validation.AutoValidationRunner = AutoValidationRunner;
    window.OTA.autoValidationRunner = autoValidationRunner;

    console.log('✅ 自动验证运行器已加载');

})();
