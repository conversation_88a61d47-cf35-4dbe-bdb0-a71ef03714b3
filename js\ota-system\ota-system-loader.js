/**
 * 依赖标签（Dependency Tags）
 * 文件: js/ota-system/ota-system-loader.js
 * 角色: OTA系统加载器（按渠道/策略加载与初始化）
 * 上游依赖(直接使用): Registry, ServiceLocator, FeatureToggle
 * 下游被依赖(常见调用方): Bootstrap/SystemIntegrator
 * 事件: 无
 * 更新时间: 2025-08-09
 */
/**
 * @OTA_SYSTEM_LOADER OTA系统统一加载器
 * 🏷️ 标签: @OTA_SYSTEM_BOOTSTRAP
 * 📝 说明: 统一加载和初始化OTA智能识别与定制化处理系统
 * <AUTHOR>
 * @version 1.0.0
 */

(function() {
    'use strict';

    /**
     * OTA系统加载器
     */
    class OTASystemLoader {
        constructor() {
            this.loadedModules = new Set();
            this.loadingPromises = new Map();
            this.initializationOrder = [
                'ota-channel-detector.js',
                'ota-customization-engine.js', 
                'config/prompt-templates.js',
                'integrations/gemini-integration.js',
                'integrations/multi-order-integration.js'
            ];
            this.logger = this.getLogger();
        }

        /**
         * 加载整个OTA系统
         * @returns {Promise<boolean>} 加载是否成功
         */
        async loadOTASystem() {
            try {
                this.logger.log('开始加载OTA智能识别与定制化处理系统', 'info');

                // 确保基础依赖存在
                await this.ensureBaseDependencies();

                // 按顺序加载模块
                for (const modulePath of this.initializationOrder) {
                    await this.loadModule(modulePath);
                }

                // 验证系统完整性
                const isValid = await this.validateSystemIntegrity();
                
                if (isValid) {
                    this.logger.log('OTA系统加载完成', 'info', {
                        加载的模块: Array.from(this.loadedModules),
                        系统状态: '就绪'
                    });
                    
                    // 触发系统就绪事件
                    this.dispatchSystemReadyEvent();
                    return true;
                } else {
                    throw new Error('系统完整性验证失败');
                }

            } catch (error) {
                this.logger.log('OTA系统加载失败', 'error', { error: error.message });
                return false;
            }
        }

        /**
         * 确保基础依赖存在
         * @returns {Promise<void>}
         */
        async ensureBaseDependencies() {
            // 等待基础服务加载
            const maxWaitTime = 5000; // 5秒超时
            const startTime = Date.now();

            while (Date.now() - startTime < maxWaitTime) {
                if (this.checkBaseDependencies()) {
                    return;
                }
                await this.sleep(100);
            }

            throw new Error('基础依赖加载超时');
        }

        /**
         * 检查基础依赖
         * @returns {boolean} 依赖是否满足
         */
        checkBaseDependencies() {
            const required = [
                'window.OTA',
                'window.OTA.otaChannelMapping' // 来自原有的 ota-channel-mapping.js
            ];

            return required.every(dep => this.getNestedProperty(window, dep));
        }

        /**
         * 加载单个模块
         * @param {string} modulePath - 模块路径
         * @returns {Promise<void>}
         */
        async loadModule(modulePath) {
            // 检查模块是否已经通过静态加载存在
            if (this.isModuleAlreadyLoaded(modulePath)) {
                this.logger.log(`模块已存在，跳过动态加载: ${modulePath}`, 'info');
                this.loadedModules.add(modulePath); // 标记为已加载
                return;
            }

            if (this.loadedModules.has(modulePath)) {
                return;
            }

            // 如果正在加载，等待加载完成
            if (this.loadingPromises.has(modulePath)) {
                return await this.loadingPromises.get(modulePath);
            }

            // 创建加载Promise
            const loadPromise = this.doLoadModule(modulePath);
            this.loadingPromises.set(modulePath, loadPromise);

            try {
                await loadPromise;
                this.loadedModules.add(modulePath);
                this.logger.log(`模块加载成功: ${modulePath}`, 'info');
            } catch (error) {
                this.logger.log(`模块加载失败: ${modulePath}`, 'error', { error: error.message });
                throw error;
            } finally {
                this.loadingPromises.delete(modulePath);
            }
        }

        /**
         * 执行模块加载
         * @param {string} modulePath - 模块路径
         * @returns {Promise<void>}
         */
        async doLoadModule(modulePath) {
            return new Promise((resolve, reject) => {
                const script = document.createElement('script');
                script.src = `js/ota-system/${modulePath}`;
                script.type = 'text/javascript';
                
                script.onload = () => {
                    resolve();
                };
                
                script.onerror = () => {
                    reject(new Error(`Failed to load module: ${modulePath}`));
                };

                document.head.appendChild(script);
            });
        }

        /**
         * 检查模块是否已经通过静态加载存在
         * @param {string} modulePath - 模块路径
         * @returns {boolean} 模块是否已存在
         */
        isModuleAlreadyLoaded(modulePath) {
            const moduleChecks = {
                'ota-channel-detector.js': () => !!(window.OTA && window.OTA.channelDetector),
                'ota-customization-engine.js': () => !!(window.OTA && window.OTA.customizationEngine),
                'config/prompt-templates.js': () => !!(window.OTA && window.OTA.promptTemplates),
                'integrations/gemini-integration.js': () => !!(window.OTA && window.OTA.otaGeminiIntegration),
                'integrations/multi-order-integration.js': () => !!(window.OTA && window.OTA.otaMultiOrderIntegration)
            };

            const checker = moduleChecks[modulePath];
            if (checker) {
                return checker();
            }

            // 对于未知模块，假设未加载
            return false;
        }

        /**
         * 验证系统完整性
         * @returns {Promise<boolean>} 验证是否通过
         */
        async validateSystemIntegrity() {
            try {
                // 检查核心组件是否存在
                const coreComponents = [
                    'window.OTA.channelDetector',
                    'window.OTA.customizationEngine',
                    'window.OTA.generateChannelPrompt',
                    'window.OTA.otaGeminiIntegration',
                    'window.OTA.otaMultiOrderIntegration'
                ];

                for (const component of coreComponents) {
                    if (!this.getNestedProperty(window, component)) {
                        this.logger.log(`缺少核心组件: ${component}`, 'error');
                        return false;
                    }
                }

                // 执行功能测试
                const testResults = await this.runFunctionalTests();
                
                return testResults.every(result => result.success);

            } catch (error) {
                this.logger.log('系统完整性验证异常', 'error', { error: error.message });
                return false;
            }
        }

        /**
         * 运行功能测试
         * @returns {Promise<array>} 测试结果
         */
        async runFunctionalTests() {
            const tests = [
                {
                    name: '渠道检测测试',
                    test: () => this.testChannelDetection()
                },
                {
                    name: '定制化引擎测试', 
                    test: () => this.testCustomizationEngine()
                },
                {
                    name: '提示词生成测试',
                    test: () => this.testPromptGeneration()
                }
            ];

            const results = [];

            for (const { name, test } of tests) {
                try {
                    await test();
                    results.push({ name, success: true });
                    this.logger.log(`✅ ${name} 通过`, 'info');
                } catch (error) {
                    results.push({ name, success: false, error: error.message });
                    this.logger.log(`❌ ${name} 失败`, 'error', { error: error.message });
                }
            }

            return results;
        }

        /**
         * 测试渠道检测功能
         */
        async testChannelDetection() {
            const testText = "CD123456AB 客户：张先生 从酒店送机到KLIA";
            const result = window.OTA.channelDetector.detectChannel(testText, "CD123456AB");
            
            if (!result.detectedChannel || result.detectedChannel !== 'Chong Dealer') {
                throw new Error('渠道检测功能异常');
            }
        }

        /**
         * 测试定制化引擎功能
         */
        async testCustomizationEngine() {
            const testOrder = {
                customer_name: "测试客户",
                passenger_count: 2,
                sub_category_id: 3
            };
            
            const result = window.OTA.customizationEngine.processOrder('Chong Dealer', testOrder);
            
            if (!result.success) {
                throw new Error('定制化引擎功能异常');
            }
        }

        /**
         * 测试提示词生成功能
         */
        async testPromptGeneration() {
            const basePrompt = "测试基础提示词";
            const customPrompt = window.OTA.generateChannelPrompt('Chong Dealer', basePrompt, false);
            
            if (!customPrompt || customPrompt === basePrompt) {
                throw new Error('提示词生成功能异常');
            }
        }

        /**
         * 触发系统就绪事件
         */
        dispatchSystemReadyEvent() {
            if (window.CustomEvent) {
                const event = new CustomEvent('otaSystemReady', {
                    detail: {
                        timestamp: new Date().toISOString(),
                        loadedModules: Array.from(this.loadedModules),
                        version: '1.0.0'
                    }
                });
                window.dispatchEvent(event);
            }
        }

        /**
         * 获取系统状态
         * @returns {object} 系统状态信息
         */
        getSystemStatus() {
            return {
                isLoaded: this.loadedModules.size === this.initializationOrder.length,
                loadedModules: Array.from(this.loadedModules),
                totalModules: this.initializationOrder.length,
                loadProgress: Math.round((this.loadedModules.size / this.initializationOrder.length) * 100),
                components: {
                    channelDetector: !!(window.OTA && window.OTA.channelDetector),
                    customizationEngine: !!(window.OTA && window.OTA.customizationEngine),
                    geminiIntegration: !!(window.OTA && window.OTA.otaGeminiIntegration),
                    multiOrderIntegration: !!(window.OTA && window.OTA.otaMultiOrderIntegration)
                }
            };
        }

        /**
         * 睡眠函数
         * @param {number} ms - 毫秒数
         * @returns {Promise<void>}
         */
        sleep(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        }

        /**
         * 获取嵌套属性
         * @param {object} obj - 对象
         * @param {string} path - 属性路径
         * @returns {any} 属性值
         */
        getNestedProperty(obj, path) {
            return path.split('.').reduce((current, key) => current && current[key], obj);
        }

        /**
         * 获取Logger实例
         * @returns {object} Logger实例
         */
        getLogger() {
            if (typeof getLogger === 'function') {
                return getLogger();
            }
            return {
                log: (message, level, data) => {
                    console.log(`[OTA-LOADER][${level.toUpperCase()}] ${message}`, data || '');
                }
            };
        }
    }

    // 创建全局实例
    const otaSystemLoader = new OTASystemLoader();

    // 暴露到OTA命名空间
    window.OTA = window.OTA || {};
    window.OTA.SystemLoader = OTASystemLoader;
    window.OTA.systemLoader = otaSystemLoader;

    // 自动加载系统（在DOM加载完成后）
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(() => otaSystemLoader.loadOTASystem(), 500);
        });
    } else {
        // DOM已经加载完成
        setTimeout(() => otaSystemLoader.loadOTASystem(), 500);
    }

    // 暴露全局加载函数供手动调用
    window.loadOTASystem = () => otaSystemLoader.loadOTASystem();
    window.getOTASystemStatus = () => otaSystemLoader.getSystemStatus();

})();