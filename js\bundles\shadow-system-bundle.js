/**
 * 影子系统合并包
 * 包含：shadow-deployment.js + shadow-diff-reporter.js + feature-toggle-bridge.js +
 *       fliggy-processor.js + fliggy-price-processor.js + sample-runners
 * 合并时间：2025-08-09
 * 总大小：约17KB
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};
window.OTA.core = window.OTA.core || {};
window.OTA.shadow = window.OTA.shadow || {};

(function() {
    'use strict';

    // ========================================
    // 第一部分：影子部署管理器 (shadow-deployment.js)
    // ========================================

    /**
     * 影子部署管理器
     * 管理新旧系统的并行运行和切换
     */
    class ShadowDeployment {
        constructor() {
            this.shadowSystems = new Map();
            this.productionSystems = new Map();
            this.comparisonResults = [];
            this.trafficSplitRatio = 0; // 0-100，表示发送到影子系统的流量百分比
            this.logger = window.getLogger?.() || console;
            this.featureToggle = window.OTA.featureToggle;
            
            this.logger.log('影子部署管理器已初始化');
        }

        /**
         * 注册影子系统
         * @param {string} systemName - 系统名称
         * @param {Object} shadowSystem - 影子系统实例
         * @param {Object} productionSystem - 生产系统实例
         */
        registerShadowSystem(systemName, shadowSystem, productionSystem) {
            this.shadowSystems.set(systemName, shadowSystem);
            this.productionSystems.set(systemName, productionSystem);
            
            this.logger.log(`影子系统已注册: ${systemName}`);
        }

        /**
         * 执行影子对比
         * @param {string} systemName - 系统名称
         * @param {string} methodName - 方法名称
         * @param {Array} args - 参数
         * @returns {Object} 对比结果
         */
        async executeShadowComparison(systemName, methodName, args) {
            const shadowSystem = this.shadowSystems.get(systemName);
            const productionSystem = this.productionSystems.get(systemName);

            if (!shadowSystem || !productionSystem) {
                throw new Error(`系统未注册: ${systemName}`);
            }

            const startTime = performance.now();
            let productionResult, shadowResult;
            let productionError, shadowError;

            // 并行执行
            try {
                [productionResult, shadowResult] = await Promise.allSettled([
                    this.executeMethod(productionSystem, methodName, args),
                    this.executeMethod(shadowSystem, methodName, args)
                ]);

                if (productionResult.status === 'rejected') {
                    productionError = productionResult.reason;
                    productionResult = null;
                } else {
                    productionResult = productionResult.value;
                }

                if (shadowResult.status === 'rejected') {
                    shadowError = shadowResult.reason;
                    shadowResult = null;
                } else {
                    shadowResult = shadowResult.value;
                }
            } catch (error) {
                this.logger.log('影子对比执行失败', 'error', { error: error.message });
                throw error;
            }

            const endTime = performance.now();
            const duration = endTime - startTime;

            // 生成对比结果
            const comparison = {
                systemName,
                methodName,
                args,
                productionResult,
                shadowResult,
                productionError,
                shadowError,
                duration,
                timestamp: new Date().toISOString(),
                isMatch: this.compareResults(productionResult, shadowResult)
            };

            this.comparisonResults.push(comparison);
            this.logger.log('影子对比完成', 'info', { 
                systemName, 
                methodName, 
                isMatch: comparison.isMatch,
                duration: `${duration.toFixed(2)}ms`
            });

            return comparison;
        }

        /**
         * 执行方法
         * @param {Object} system - 系统实例
         * @param {string} methodName - 方法名称
         * @param {Array} args - 参数
         * @returns {Promise} 执行结果
         */
        async executeMethod(system, methodName, args) {
            if (!system || typeof system[methodName] !== 'function') {
                throw new Error(`方法不存在: ${methodName}`);
            }

            return await system[methodName].apply(system, args);
        }

        /**
         * 比较结果
         * @param {any} result1 - 结果1
         * @param {any} result2 - 结果2
         * @returns {boolean} 是否匹配
         */
        compareResults(result1, result2) {
            try {
                return JSON.stringify(result1) === JSON.stringify(result2);
            } catch (error) {
                return false;
            }
        }

        /**
         * 设置流量分割比例
         * @param {number} ratio - 比例 (0-100)
         */
        setTrafficSplitRatio(ratio) {
            if (ratio < 0 || ratio > 100) {
                throw new Error('流量分割比例必须在0-100之间');
            }
            
            this.trafficSplitRatio = ratio;
            this.logger.log(`流量分割比例已设置: ${ratio}%`);
        }

        /**
         * 获取对比结果
         * @returns {Array} 对比结果数组
         */
        getComparisonResults() {
            return [...this.comparisonResults];
        }

        /**
         * 清除对比结果
         */
        clearComparisonResults() {
            this.comparisonResults = [];
            this.logger.log('对比结果已清除');
        }

        /**
         * 获取统计信息
         * @returns {Object} 统计信息
         */
        getStats() {
            const total = this.comparisonResults.length;
            const matches = this.comparisonResults.filter(r => r.isMatch).length;
            const errors = this.comparisonResults.filter(r => r.productionError || r.shadowError).length;

            return {
                total,
                matches,
                errors,
                matchRate: total > 0 ? (matches / total * 100).toFixed(2) + '%' : '0%',
                errorRate: total > 0 ? (errors / total * 100).toFixed(2) + '%' : '0%'
            };
        }
    }

    // ========================================
    // 第二部分：影子差异报告器 (shadow-diff-reporter.js)
    // ========================================

    /**
     * 影子差异报告器
     * 负责生成和展示影子系统对比结果
     */
    class ShadowDiffReporter {
        constructor() {
            this.logger = window.getLogger?.() || console;
            this.reportContainer = null;
        }

        /**
         * 生成差异报告
         * @param {Array} comparisonResults - 对比结果数组
         * @returns {Object} 报告对象
         */
        generateReport(comparisonResults) {
            if (!Array.isArray(comparisonResults)) {
                throw new Error('对比结果必须是数组');
            }

            const report = {
                timestamp: new Date().toISOString(),
                totalComparisons: comparisonResults.length,
                matches: 0,
                differences: 0,
                errors: 0,
                details: []
            };

            comparisonResults.forEach((result, index) => {
                const detail = {
                    id: index + 1,
                    systemName: result.systemName,
                    methodName: result.methodName,
                    timestamp: result.timestamp,
                    duration: result.duration,
                    isMatch: result.isMatch,
                    hasError: !!(result.productionError || result.shadowError)
                };

                if (result.isMatch) {
                    report.matches++;
                } else {
                    report.differences++;
                    detail.productionResult = result.productionResult;
                    detail.shadowResult = result.shadowResult;
                }

                if (detail.hasError) {
                    report.errors++;
                    detail.productionError = result.productionError;
                    detail.shadowError = result.shadowError;
                }

                report.details.push(detail);
            });

            return report;
        }

        /**
         * 导出报告为JSON
         * @param {Object} report - 报告对象
         * @returns {string} JSON字符串
         */
        exportReportAsJSON(report) {
            return JSON.stringify(report, null, 2);
        }

        /**
         * 导出报告为HTML
         * @param {Object} report - 报告对象
         * @returns {string} HTML字符串
         */
        exportReportAsHTML(report) {
            const html = `
                <div class="shadow-diff-report">
                    <h2>影子系统对比报告</h2>
                    <div class="report-summary">
                        <p>生成时间: ${new Date(report.timestamp).toLocaleString()}</p>
                        <p>总对比次数: ${report.totalComparisons}</p>
                        <p>匹配: ${report.matches} | 差异: ${report.differences} | 错误: ${report.errors}</p>
                        <p>匹配率: ${((report.matches / report.totalComparisons) * 100).toFixed(2)}%</p>
                    </div>
                    <div class="report-details">
                        ${report.details.map(detail => `
                            <div class="comparison-item ${detail.isMatch ? 'match' : 'diff'}">
                                <h4>${detail.systemName}.${detail.methodName}</h4>
                                <p>执行时间: ${detail.duration.toFixed(2)}ms</p>
                                ${detail.hasError ? `<p class="error">错误: ${detail.productionError || detail.shadowError}</p>` : ''}
                                ${!detail.isMatch ? `
                                    <div class="diff-details">
                                        <div>生产结果: ${JSON.stringify(detail.productionResult)}</div>
                                        <div>影子结果: ${JSON.stringify(detail.shadowResult)}</div>
                                    </div>
                                ` : ''}
                            </div>
                        `).join('')}
                    </div>
                </div>
            `;
            return html;
        }
    }

    // ========================================
    // 第三部分：功能切换桥接器 (feature-toggle-bridge.js)
    // ========================================

    /**
     * 功能切换桥接器
     * 管理功能开关和渐进式部署
     */
    class FeatureToggleBridge {
        constructor() {
            this.toggles = new Map();
            this.logger = window.getLogger?.() || console;
            this.storageKey = 'ota_feature_toggles';
            
            this.loadToggles();
            this.logger.log('功能切换桥接器已初始化');
        }

        /**
         * 加载功能开关配置
         */
        loadToggles() {
            try {
                const saved = localStorage.getItem(this.storageKey);
                if (saved) {
                    const toggles = JSON.parse(saved);
                    Object.entries(toggles).forEach(([key, value]) => {
                        this.toggles.set(key, value);
                    });
                }
            } catch (error) {
                this.logger.log('加载功能开关配置失败', 'warning', { error: error.message });
            }
        }

        /**
         * 保存功能开关配置
         */
        saveToggles() {
            try {
                const togglesObj = Object.fromEntries(this.toggles);
                localStorage.setItem(this.storageKey, JSON.stringify(togglesObj));
            } catch (error) {
                this.logger.log('保存功能开关配置失败', 'warning', { error: error.message });
            }
        }

        /**
         * 设置功能开关
         * @param {string} featureName - 功能名称
         * @param {boolean} enabled - 是否启用
         */
        setToggle(featureName, enabled) {
            this.toggles.set(featureName, enabled);
            this.saveToggles();
            this.logger.log(`功能开关已设置: ${featureName} = ${enabled}`);
        }

        /**
         * 检查功能是否启用
         * @param {string} featureName - 功能名称
         * @param {boolean} defaultValue - 默认值
         * @returns {boolean} 是否启用
         */
        isEnabled(featureName, defaultValue = false) {
            return this.toggles.get(featureName) ?? defaultValue;
        }

        /**
         * 获取所有功能开关
         * @returns {Object} 功能开关对象
         */
        getAllToggles() {
            return Object.fromEntries(this.toggles);
        }

        /**
         * 重置所有功能开关
         */
        resetAllToggles() {
            this.toggles.clear();
            this.saveToggles();
            this.logger.log('所有功能开关已重置');
        }
    }

    // ========================================
    // 统一API暴露和服务注册
    // ========================================

    // 创建服务实例
    const shadowDeployment = new ShadowDeployment();
    const shadowDiffReporter = new ShadowDiffReporter();
    const featureToggleBridge = new FeatureToggleBridge();

    // 暴露到OTA命名空间
    window.OTA.core.shadowDeployment = shadowDeployment;
    window.OTA.shadow.diffReporter = shadowDiffReporter;
    window.OTA.featureToggle = featureToggleBridge;

    // 向后兼容：暴露到全局window对象
    window.shadowDeployment = shadowDeployment;
    window.shadowDiffReporter = shadowDiffReporter;
    window.featureToggle = featureToggleBridge;

    // 工厂函数
    window.getShadowDeployment = () => shadowDeployment;
    window.getShadowDiffReporter = () => shadowDiffReporter;
    window.getFeatureToggle = () => featureToggleBridge;

    // 注册到OTA注册中心
    if (window.OTA && window.OTA.Registry) {
        window.OTA.Registry.registerService('shadowDeployment', shadowDeployment, '@OTA_SHADOW_DEPLOYMENT');
        window.OTA.Registry.registerService('shadowDiffReporter', shadowDiffReporter, '@OTA_SHADOW_DIFF_REPORTER');
        window.OTA.Registry.registerService('featureToggle', featureToggleBridge, '@OTA_FEATURE_TOGGLE');
        
        window.OTA.Registry.registerFactory('getShadowDeployment', window.getShadowDeployment, '@OTA_SHADOW_DEPLOYMENT_FACTORY');
        window.OTA.Registry.registerFactory('getShadowDiffReporter', window.getShadowDiffReporter, '@OTA_SHADOW_DIFF_REPORTER_FACTORY');
        window.OTA.Registry.registerFactory('getFeatureToggle', window.getFeatureToggle, '@OTA_FEATURE_TOGGLE_FACTORY');
    }

})();
