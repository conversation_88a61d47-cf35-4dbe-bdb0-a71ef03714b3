/**
 * 依赖标签（Dependency Tags）
 * 文件: js/strategies/jingge-ota-strategy.js
 * 角色: JingGe渠道策略（轻量级：提示词片段/车型映射）
 * 上游依赖(直接使用): Logger（可选）
 * 下游被依赖(常见调用方): Gemini Integration, Customization Engine
 * 事件: 无
 * 更新时间: 2025-08-09
 */
/**
 * JingGe OTA策略 - 纯静态提示词片段提供者
 *
 * 设计理念：策略文件只提供渠道特定的提示词片段，不承担任何数据处理职责
 * 核心职责：为AI提供JingGe渠道特定的字段级提示词片段
 *
 * 不再继承BaseOTAStrategy，成为纯静态工具类
 */

// 确保命名空间
window.OTA = window.OTA || {};

(function(){
  'use strict';

  class JingGeOTAStrategy {

    /**
     * 获取策略的渠道名称
     * @returns {string} 渠道名称
     */
    static getChannelName() {
      return 'jingge';
    }

    /**
     * 从文本内容检测JingGe渠道特征
     * 供渠道检测器调用的静态方法
     * @param {string} text - 输入文本
     * @returns {object} 检测结果 {detectedChannel, confidence, details}
     */
    static detectFromContent(text) {
      const result = {
        detectedChannel: null,
        confidence: 0,
        details: []
      };

      if (!text || typeof text !== 'string') {
        return result;
      }

      // JingGe渠道特征检测（可根据实际特征调整）
      // 目前使用基础的关键词检测，未来可以根据实际JingGe订单格式优化
      const jinggeKeywords = ['jingge', 'jinggeshop', '精格', '精格商铺'];
      const lowerText = text.toLowerCase();

      for (const keyword of jinggeKeywords) {
        if (lowerText.includes(keyword)) {
          result.detectedChannel = 'jingge';
          result.confidence = 0.85;
          result.details.push({
            type: 'jingge_keyword_match',
            keyword: keyword,
            description: 'JingGe渠道关键词识别'
          });
          break;
        }
      }

      return result;
    }

    // 字段级提示词片段（抽取/格式引导）
    static getFieldPromptSnippets(_ctx = {}){
      return {
        // 渠道名称固定返回
        ota: '渠道识别：请识别这是JingGe商铺渠道的订单，输出JSON时ota字段请设置为"Jing Ge"。',
        // 价格、车型ID、联系方式、订单号
        ota_price: '价格识别与换算：若为JingGe商铺订单，最终价格=基础价×0.615；保留两位小数，明确输出最终价；无法确定时仅输出基础价并标注原因，不要猜测。',
        car_type_id: '车型ID映射（根据中文车型名称）：请从订单文本中识别车型名称，然后按以下映射返回对应ID：经济型/舒适型/五座→5；经济型/舒适型/七座→35；商务七座/商务型七座→31；豪华七座/豪华型七座→32；商务九座/商务型九座→20；中巴/小巴→24。输出JSON时请设置car_type_id为上述ID之一的整数，若无法识别车型名称则返回null，不要猜测或输出名称。',
        customer_contact: '若订单未提供手机号，可临时使用订单号(ota_reference_number)作为联系标识填充customer_contact字段；若存在手机号，请保持原值，不要覆盖。',
        ota_reference_number: '订单号识别：请从文本中抽取明确的订单编号；一般为纯数字组合，若无可靠线索，请返回null，不要凭空生成。'
      };
    }

    // 统一车型映射（名称→ID）
    static getVehicleIdMapping(){
      return {
        'economy_comfort_5': 5,     // 经济/舒适 五座
        'economy_comfort_7': 35,    // 经济/舒适 七座
        'business_7': 31,           // 商务七座
        'luxury_7': 32,             // 豪华七座
        'business_9': 20,           // 商务九座
        'minibus': 24               // 中巴
      };
    }
  }

  // 导出
  if (typeof module !== 'undefined' && module.exports){
    module.exports = JingGeOTAStrategy;
  } else if (typeof window !== 'undefined'){
    window.JingGeOTAStrategy = JingGeOTAStrategy;
  }

  console.log('✅ JingGeOTAStrategy (重构版) 已加载');
})();
