/**
 * 全局字段标准化拦截层
 * 基于GoMyHire API标准规范，实现非侵入式的字段统一映射
 * 
 * 核心功能：
 * 1. 自动拦截所有数据流，进行字段名称标准化
 * 2. 保证从数据源到API发送的整个数据流中字段名称完全一致
 * 3. 清理所有导致数据转换的冗余逻辑
 * 4. 消除字段名称不一致导致的数据映射错误
 * 
 * 设计原则：
 * - 非侵入性：不修改现有代码，通过拦截器实现
 * - 双向映射：支持API字段↔前端字段的双向转换
 * - 向后兼容：支持所有历史遗留字段格式
 * - 性能优化：缓存映射结果，减少重复计算
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025-01-08
 */

(function() {
    'use strict';

    // 防止重复加载
    if (window.OTA && window.OTA.GlobalFieldStandardizationLayer) {
        console.log('[GlobalFieldStandardization] 字段标准化层已存在，跳过重复加载');
        return;
    }

    /**
     * 全局字段标准化拦截层类
     */
    class GlobalFieldStandardizationLayer {
        constructor() {
            this.initialized = false;
            this.interceptors = new Map();
            this.cache = new Map();
            this.statistics = {
                transformations: 0,
                cacheHits: 0,
                errors: 0
            };
            
            // 初始化标准字段映射表
            this.initializeStandardMappings();
            // 减法开发：默认不自动安装拦截器，受特性开关控制
        }

        /**
         * 初始化标准字段映射配置
         */
        initializeStandardMappings() {
            // GoMyHire API标准字段（snake_case）
            this.API_STANDARD_FIELDS = {
                'customer_name': 'customer_name',
                'customer_contact': 'customer_contact', 
                'customer_email': 'customer_email',
                'ota_reference_number': 'ota_reference_number',
                'flight_info': 'flight_info',
                'date': 'date',
                'time': 'time',
                'pickup': 'pickup',
                'destination': 'destination',
                'car_type_id': 'car_type_id',
                'sub_category_id': 'sub_category_id',
                'driving_region_id': 'driving_region_id',
                'passenger_number': 'passenger_number',
                'luggage_number': 'luggage_number',
                'languages_id_array': 'languages_id_array',
                'extra_requirement': 'extra_requirement',
                'baby_chair': 'baby_chair',
                'tour_guide': 'tour_guide',
                'meet_and_greet': 'meet_and_greet',
                'ota_price': 'ota_price',
                'currency': 'currency',
                'incharge_by_backend_user_id': 'incharge_by_backend_user_id',
                'ota': 'ota',
                'raw_text': 'raw_text'
            };

            // 统一字段映射表（包含所有变体到API标准字段的映射）
            this.UNIFIED_FIELD_MAPPING = {
                // === 客户信息字段统一 ===
                'customerName': 'customer_name',
                'customer_name': 'customer_name',
                'customerContact': 'customer_contact',
                'customer_contact': 'customer_contact',
                'customerEmail': 'customer_email',
                'customer_email': 'customer_email',

                // === 订单基础信息统一 ===
                'otaReferenceNumber': 'ota_reference_number',
                'ota_reference_number': 'ota_reference_number',
                'flightInfo': 'flight_info',
                'flight_info': 'flight_info',
                'flight_number': 'flight_info',

                // === 时间字段统一 ===
                'pickupDate': 'date',
                'pickup_date': 'date',
                'date': 'date',
                'pickupTime': 'time',
                'pickup_time': 'time',
                'time': 'time',

                // === 地点字段统一 ===
                'pickupLocation': 'pickup',
                'pickup_location': 'pickup',
                'pickup': 'pickup',
                'dropoffLocation': 'destination',
                'dropoff_location': 'destination',
                'dropoff': 'destination',
                'destination': 'destination',

                // === 车型字段统一 ===
                'carTypeId': 'car_type_id',
                'car_type_id': 'car_type_id',
                'carType': 'car_type_id',
                'car_type': 'car_type_id',
                'vehicleType': 'car_type_id',
                'vehicle_type': 'car_type_id',
                'vehicle': 'car_type_id',

                // === 区域字段统一 ===
                'drivingRegionId': 'driving_region_id',
                'driving_region_id': 'driving_region_id',
                'drivingRegion': 'driving_region_id',
                'driving_region': 'driving_region_id',
                'region': 'driving_region_id',
                'area': 'driving_region_id',

                // === 服务类别统一 ===
                'subCategoryId': 'sub_category_id',
                'sub_category_id': 'sub_category_id',

                // === 乘客和行李字段统一 ===
                'passengerCount': 'passenger_number',
                'passenger_count': 'passenger_number',
                'passenger_number': 'passenger_number',
                'luggageCount': 'luggage_number',
                'luggage_count': 'luggage_number',
                'luggage_number': 'luggage_number',

                // === 语言字段统一 ===
                'languagesIdArray': 'languages_id_array',
                'languages_id_array': 'languages_id_array',

                // === 额外需求统一 ===
                'extraRequirement': 'extra_requirement',
                'extra_requirement': 'extra_requirement',

                // === 服务选项统一 ===
                'babyChair': 'baby_chair',
                'baby_chair': 'baby_chair',
                'tourGuide': 'tour_guide',
                'tour_guide': 'tour_guide',
                'meetAndGreet': 'meet_and_greet',
                'meet_and_greet': 'meet_and_greet',

                // === 价格字段统一 ===
                'otaPrice': 'ota_price',
                'ota_price': 'ota_price',
                'price': 'ota_price',
                'cost': 'ota_price',
                'amount': 'ota_price',

                // === 货币字段统一 ===
                'currency': 'currency',

                // === 系统字段统一 ===
                'rawText': 'raw_text',
                'raw_text': 'raw_text',
                'ota': 'ota',
                'incharge_by_backend_user_id': 'incharge_by_backend_user_id'
            };

            // 反向映射：API字段 → 前端字段
            this.API_TO_FRONTEND_MAPPING = {
                'customer_name': 'customerName',
                'customer_contact': 'customerContact',
                'customer_email': 'customerEmail',
                'ota_reference_number': 'otaReferenceNumber',
                'flight_info': 'flightInfo',
                'date': 'pickupDate',
                'time': 'pickupTime',
                'pickup': 'pickupLocation',
                'destination': 'dropoffLocation',
                'car_type_id': 'carTypeId',
                'sub_category_id': 'subCategoryId',
                'driving_region_id': 'drivingRegionId',
                'passenger_number': 'passengerCount',
                'luggage_number': 'luggageCount',
                'languages_id_array': 'languagesIdArray',
                'extra_requirement': 'extraRequirement',
                'baby_chair': 'babyChair',
                'tour_guide': 'tourGuide',
                'meet_and_greet': 'meetAndGreet',
                'ota_price': 'otaPrice',
                'currency': 'currency',
                'raw_text': 'rawText',
                'ota': 'ota',
                'incharge_by_backend_user_id': 'inchargeByBackendUserId'
            };

            // 可忽略的字段（系统内部字段，不需要映射）
            this.IGNORABLE_FIELDS = new Set([
                '_otaChannel', 'needsPagingService', 'confidence', 'analysis',
                'timestamp', 'source', 'isMultiOrder', 'orderCount',
                'originalData', '_metadata', '__proto__', 'constructor'
            ]);
        }

        /**
         * 初始化拦截器系统
         */
        initialize() {
            if (this.initialized) {
                console.warn('[GlobalFieldStandardization] 拦截器已初始化，跳过');
                return;
            }

            // 若未开启开关则不安装
            const enabled = window.OTA?.featureToggle?.isEnabled('enableGlobalFieldStandardization');
            if (!enabled) {
                console.log('[GlobalFieldStandardization] 已加载（未启用，受特性开关控制）');
                return;
            }

            // 拦截Gemini服务输出
            this.interceptGeminiService();
            
            // 拦截API服务输入
            this.interceptApiService();
            
            // 拦截多订单管理器
            this.interceptMultiOrderManager();
            
            // 拦截表单管理器
            this.interceptFormManager();

            this.initialized = true;
            console.log('[GlobalFieldStandardization] ✅ 字段标准化拦截层初始化完成');
        }

        /**
         * 拦截Gemini服务的数据输出
         */
        interceptGeminiService() {
            const self = this;
            let attempts = 0;
            const maxAttempts = 50; // 最多等待5秒

            // 等待Gemini服务“真实实例”加载，避免过早调用全局 getter 触发降级实例
            const interceptGemini = () => {
                attempts++;

                // 仅当真实实例已挂载到全局时才进行拦截安装
                const geminiService = (window.OTA && window.OTA.geminiService) || window.geminiService || null;
                if (geminiService && typeof geminiService.parseOrdersFromText === 'function') {
                    const originalParse = geminiService.parseOrdersFromText.bind(geminiService);

                    geminiService.parseOrdersFromText = function(text, options = {}) {
                        return originalParse(text, options).then(result => {
                            // 标准化Gemini输出的字段
                            if (result && result.orders) {
                                result.orders = result.orders.map(order =>
                                    self.standardizeToApiFields(order, 'gemini-output')
                                );
                            }
                            return result;
                        });
                    };

                    console.log('[GlobalFieldStandardization] ✅ Gemini服务拦截器已安装');
                    return;
                }

                if (attempts < maxAttempts) {
                    setTimeout(interceptGemini, 100);
                } else {
                    console.log('[GlobalFieldStandardization] ⚠️ Gemini服务拦截器安装超时，跳过');
                }
            };

            interceptGemini();
        }

        /**
         * 拦截API服务的数据输入
         */
        interceptApiService() {
            const self = this;
            let attempts = 0;
            const maxAttempts = 50; // 最多等待5秒

            const interceptApi = () => {
                attempts++;

                // 仅当真实实例已挂载到全局时才进行拦截安装
                const apiService = (window.OTA && window.OTA.apiService) || window.apiService || null;
                if (apiService && typeof apiService.createOrder === 'function') {
                    const originalCreateOrder = apiService.createOrder.bind(apiService);

                    apiService.createOrder = function(orderData) {
                        // 在发送到API之前标准化字段
                        const standardizedData = self.standardizeToApiFields(orderData, 'api-input');
                        return originalCreateOrder(standardizedData);
                    };

                    console.log('[GlobalFieldStandardization] ✅ API服务拦截器已安装');
                    return;
                }

                if (attempts < maxAttempts) {
                    setTimeout(interceptApi, 100);
                } else {
                    console.log('[GlobalFieldStandardization] ⚠️ API服务拦截器安装超时，跳过');
                }
            };

            interceptApi();
        }

        /**
         * 拦截多订单管理器
         */
        interceptMultiOrderManager() {
            const self = this;
            let attempts = 0;
            const maxAttempts = 50; // 最多等待5秒
            
            const interceptMultiOrder = () => {
                attempts++;
                
                if (window.OTA && window.OTA.multiOrderManager) {
                    const manager = window.OTA.multiOrderManager;
                    
                    // 拦截数据处理方法
                    if (manager.processOrderData) {
                        const originalProcess = manager.processOrderData.bind(manager);
                        manager.processOrderData = function(orderData) {
                            const standardizedData = self.standardizeToApiFields(orderData, 'multi-order');
                            return originalProcess(standardizedData);
                        };
                    }

                    // 拦截显示函数的数据输入，确保字段标准化后传入
                    if (manager.getVehicleTypeDisplay) {
                        const originalGetVehicleType = manager.getVehicleTypeDisplay.bind(manager);
                        manager.getVehicleTypeDisplay = function(order) {
                            // 标准化订单数据，确保包含正确的字段名
                            const standardizedOrder = self.standardizeToApiFields(order, 'vehicle-display');
                            return originalGetVehicleType(standardizedOrder);
                        };
                    }

                    if (manager.getDrivingRegionDisplay) {
                        const originalGetDrivingRegion = manager.getDrivingRegionDisplay.bind(manager);
                        manager.getDrivingRegionDisplay = function(order) {
                            // 标准化订单数据，确保包含正确的字段名
                            const standardizedOrder = self.standardizeToApiFields(order, 'region-display');
                            return originalGetDrivingRegion(standardizedOrder);
                        };
                    }
                    
                    console.log('[GlobalFieldStandardization] ✅ 多订单管理器拦截器已安装');
                    return;
                }
                
                if (attempts < maxAttempts) {
                    setTimeout(interceptMultiOrder, 100);
                } else {
                    console.log('[GlobalFieldStandardization] ⚠️ 多订单管理器拦截器安装超时，跳过');
                }
            };
            
            interceptMultiOrder();
        }

        /**
         * 拦截表单管理器
         */
        interceptFormManager() {
            const self = this;
            let attempts = 0;
            const maxAttempts = 50; // 最多等待5秒
            
            const interceptForm = () => {
                attempts++;
                
                if (window.getFormManager && typeof window.getFormManager === 'function') {
                    const formManager = window.getFormManager();
                    if (formManager && formManager.getFormData) {
                        const originalGetFormData = formManager.getFormData.bind(formManager);
                        
                        formManager.getFormData = function() {
                            const formData = originalGetFormData();
                            return self.standardizeToApiFields(formData, 'form-data');
                        };
                        
                        console.log('[GlobalFieldStandardization] ✅ 表单管理器拦截器已安装');
                        return;
                    }
                }
                
                if (attempts < maxAttempts) {
                    setTimeout(interceptForm, 100);
                } else {
                    console.log('[GlobalFieldStandardization] ⚠️ 表单管理器拦截器安装超时，跳过');
                }
            };
            
            interceptForm();
        }

        /**
         * 将对象字段标准化为API格式
         * @param {Object} data - 原始数据对象
         * @param {String} context - 调用上下文（用于日志和缓存）
         * @returns {Object} 标准化后的数据对象
         */
        standardizeToApiFields(data, context = 'unknown') {
            if (!data || typeof data !== 'object') {
                return data;
            }

            // 生成缓存键
            const cacheKey = `${context}_${JSON.stringify(data)}`;
            if (this.cache.has(cacheKey)) {
                this.statistics.cacheHits++;
                return this.cache.get(cacheKey);
            }

            try {
                const standardized = {};
                let transformCount = 0;

                Object.keys(data).forEach(originalField => {
                    // 跳过可忽略的字段
                    if (this.IGNORABLE_FIELDS.has(originalField)) {
                        return;
                    }

                    let standardField = originalField;
                    
                    // 检查是否需要字段映射
                    if (this.UNIFIED_FIELD_MAPPING[originalField]) {
                        standardField = this.UNIFIED_FIELD_MAPPING[originalField];
                        transformCount++;
                        
                        // 记录字段转换（仅在调试模式）
                        if (window.location?.hostname === 'localhost' && originalField !== standardField) {
                            console.log(`[FieldStandardization][${context}] ${originalField} → ${standardField}`);
                        }
                    }

                    // 复制值到标准化字段
                    standardized[standardField] = data[originalField];
                });

                // 更新统计信息
                this.statistics.transformations += transformCount;

                // 缓存结果（限制缓存大小）
                if (this.cache.size < 1000) {
                    this.cache.set(cacheKey, standardized);
                }

                return standardized;

            } catch (error) {
                this.statistics.errors++;
                console.error(`[GlobalFieldStandardization] 字段标准化失败 [${context}]:`, error);
                return data; // 返回原始数据作为后备
            }
        }

        /**
         * 将对象字段标准化为前端格式
         * @param {Object} data - API格式数据对象
         * @param {String} context - 调用上下文
         * @returns {Object} 前端格式的数据对象
         */
        standardizeToFrontendFields(data, context = 'unknown') {
            if (!data || typeof data !== 'object') {
                return data;
            }

            try {
                const standardized = {};

                Object.keys(data).forEach(apiField => {
                    // 跳过可忽略的字段
                    if (this.IGNORABLE_FIELDS.has(apiField)) {
                        return;
                    }

                    let frontendField = apiField;
                    
                    // 检查API字段到前端字段的映射
                    if (this.API_TO_FRONTEND_MAPPING[apiField]) {
                        frontendField = this.API_TO_FRONTEND_MAPPING[apiField];
                    }

                    standardized[frontendField] = data[apiField];
                });

                return standardized;

            } catch (error) {
                this.statistics.errors++;
                console.error(`[GlobalFieldStandardization] 前端字段标准化失败 [${context}]:`, error);
                return data;
            }
        }

        /**
         * 验证字段是否符合API标准
         * @param {String} fieldName - 字段名
         * @returns {Object} 验证结果
         */
        validateFieldStandard(fieldName) {
            const isApiStandard = this.API_STANDARD_FIELDS.hasOwnProperty(fieldName);
            const hasMapping = this.UNIFIED_FIELD_MAPPING.hasOwnProperty(fieldName);
            const suggestedStandard = hasMapping ? this.UNIFIED_FIELD_MAPPING[fieldName] : fieldName;

            return {
                isApiStandard,
                hasMapping,
                fieldName,
                suggestedStandard,
                needsTransformation: fieldName !== suggestedStandard
            };
        }

        /**
         * 获取字段映射统计信息
         * @returns {Object} 统计信息
         */
        getStatistics() {
            return {
                ...this.statistics,
                cacheSize: this.cache.size,
                totalMappings: Object.keys(this.UNIFIED_FIELD_MAPPING).length,
                apiStandardFields: Object.keys(this.API_STANDARD_FIELDS).length
            };
        }

        /**
         * 清理缓存
         */
        clearCache() {
            this.cache.clear();
            console.log('[GlobalFieldStandardization] 缓存已清理');
        }

        /**
         * 生成字段映射报告
         * @returns {Object} 字段映射报告
         */
        generateMappingReport() {
            const report = {
                summary: {
                    totalTransformations: this.statistics.transformations,
                    cacheHits: this.statistics.cacheHits,
                    errors: this.statistics.errors,
                    cacheSize: this.cache.size
                },
                fieldMappings: {
                    total: Object.keys(this.UNIFIED_FIELD_MAPPING).length,
                    apiStandard: Object.keys(this.API_STANDARD_FIELDS).length,
                    ignorable: this.IGNORABLE_FIELDS.size
                },
                activeMappings: Object.entries(this.UNIFIED_FIELD_MAPPING)
                    .filter(([from, to]) => from !== to)
                    .reduce((acc, [from, to]) => {
                        acc[from] = to;
                        return acc;
                    }, {}),
                generatedAt: new Date().toISOString()
            };

            return report;
        }
    }

    // 创建全局实例并自动初始化
    const globalFieldStandardizationLayer = new GlobalFieldStandardizationLayer();

    // 注册到OTA命名空间
    window.OTA = window.OTA || {};
    window.OTA.GlobalFieldStandardizationLayer = GlobalFieldStandardizationLayer;
    window.OTA.globalFieldStandardizationLayer = globalFieldStandardizationLayer;

    // 全局访问接口
    window.getGlobalFieldStandardizationLayer = () => globalFieldStandardizationLayer;

    // 提供便捷的全局方法
    window.standardizeFieldsToApi = (data, context) => 
        globalFieldStandardizationLayer.standardizeToApiFields(data, context);
    
    window.standardizeFieldsToFrontend = (data, context) => 
        globalFieldStandardizationLayer.standardizeToFrontendFields(data, context);

    // 受控启用/禁用
    window.enableGlobalFieldStandardization = () => globalFieldStandardizationLayer.initialize();
    window.disableGlobalFieldStandardization = () => {
        if (window.OTA?.featureToggle) {
            window.OTA.featureToggle.disable('enableGlobalFieldStandardization');
        }
        globalFieldStandardizationLayer.initialized = false;
        console.log('[GlobalFieldStandardization] 已禁用');
    };

    console.log('[GlobalFieldStandardization] ✅ 全局字段标准化拦截层已加载并自动初始化');

})();