/**
 * 小服务文件合并包
 * 包含：currency-converter.js + flight-info-service.js + address-translation-service.js
 * 合并时间：2025-08-09
 * 总大小：约24KB
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};

(function() {
    'use strict';

    // ========================================
    // 第一部分：货币转换服务 (currency-converter.js)
    // ========================================

    /**
     * 货币转换管理器
     * 负责价格识别、货币转换和汇率管理
     */
    class CurrencyConverter {
        constructor() {
            this.storageKey = 'ota_exchange_rates';
            this.baseCurrency = 'MYR'; // 基础货币为马来西亚令吉
            
            // 默认汇率（可由用户自定义）
            this.defaultRates = {
                'CNY': 0.615,  // 人民币转MYR：乘以0.615
                'USD': 4.3,    // 美元转MYR：乘以4.3
                'SGD': 3.4,    // 新币转MYR：乘以3.4
                'MYR': 1.0     // MYR保持不变
            };
            
            this.init();
        }

        /**
         * 获取国际化管理器
         */
        getI18nManager() {
            return (window.OTA && window.OTA.i18nManager) || window.i18nManager;
        }

        /**
         * 初始化货币转换器
         */
        init() {
            this.loadExchangeRates();
            const logger = window.getLogger?.();
            if (logger) {
                logger.log('货币转换管理器已初始化', 'info');
            }
        }

        /**
         * 加载汇率设置
         */
        loadExchangeRates() {
            try {
                const savedRates = localStorage.getItem(this.storageKey);
                if (savedRates) {
                    const rates = JSON.parse(savedRates);
                    this.defaultRates = { ...this.defaultRates, ...rates };
                }
            } catch (error) {
                console.warn('加载汇率设置失败，使用默认汇率:', error);
            }
        }

        /**
         * 保存汇率设置
         */
        saveExchangeRates() {
            try {
                localStorage.setItem(this.storageKey, JSON.stringify(this.defaultRates));
            } catch (error) {
                console.warn('保存汇率设置失败:', error);
            }
        }

        /**
         * 识别文本中的价格和货币
         * @param {string} text - 输入文本
         * @returns {Array} 识别到的价格信息数组
         */
        extractPricesFromText(text) {
            if (!text) return [];

            const pricePatterns = [
                // 人民币模式
                /(?:¥|RMB|CNY|人民币)\s*(\d+(?:\.\d{1,2})?)/gi,
                /(\d+(?:\.\d{1,2})?)\s*(?:¥|RMB|CNY|人民币)/gi,
                
                // 美元模式
                /(?:\$|USD|美元)\s*(\d+(?:\.\d{1,2})?)/gi,
                /(\d+(?:\.\d{1,2})?)\s*(?:\$|USD|美元)/gi,
                
                // 新币模式
                /(?:S\$|SGD|新币)\s*(\d+(?:\.\d{1,2})?)/gi,
                /(\d+(?:\.\d{1,2})?)\s*(?:S\$|SGD|新币)/gi,
                
                // 马币模式
                /(?:RM|MYR|马币)\s*(\d+(?:\.\d{1,2})?)/gi,
                /(\d+(?:\.\d{1,2})?)\s*(?:RM|MYR|马币)/gi
            ];

            const results = [];
            
            pricePatterns.forEach(pattern => {
                let match;
                while ((match = pattern.exec(text)) !== null) {
                    const amount = parseFloat(match[1]);
                    if (!isNaN(amount) && amount > 0) {
                        // 根据模式确定货币类型
                        let currency = 'MYR';
                        const matchText = match[0].toLowerCase();
                        
                        if (matchText.includes('¥') || matchText.includes('rmb') || matchText.includes('cny') || matchText.includes('人民币')) {
                            currency = 'CNY';
                        } else if (matchText.includes('$') || matchText.includes('usd') || matchText.includes('美元')) {
                            currency = 'USD';
                        } else if (matchText.includes('s$') || matchText.includes('sgd') || matchText.includes('新币')) {
                            currency = 'SGD';
                        }
                        
                        results.push({
                            amount,
                            currency,
                            originalText: match[0],
                            position: match.index
                        });
                    }
                }
            });

            // 去重并排序
            return results
                .filter((item, index, self) => 
                    index === self.findIndex(t => t.amount === item.amount && t.currency === item.currency)
                )
                .sort((a, b) => a.position - b.position);
        }

        /**
         * 转换货币
         * @param {number} amount - 金额
         * @param {string} fromCurrency - 源货币
         * @param {string} toCurrency - 目标货币
         * @returns {number} 转换后的金额
         */
        convertCurrency(amount, fromCurrency, toCurrency = this.baseCurrency) {
            if (!amount || isNaN(amount)) return 0;
            if (fromCurrency === toCurrency) return amount;

            const fromRate = this.defaultRates[fromCurrency];
            const toRate = this.defaultRates[toCurrency];

            if (!fromRate || !toRate) {
                console.warn(`不支持的货币转换: ${fromCurrency} -> ${toCurrency}`);
                return amount;
            }

            // 先转换为基础货币，再转换为目标货币
            const baseAmount = fromCurrency === this.baseCurrency ? amount : amount * fromRate;
            const result = toCurrency === this.baseCurrency ? baseAmount : baseAmount / toRate;

            return Math.round(result * 100) / 100; // 保留两位小数
        }

        /**
         * 格式化价格显示
         * @param {number} amount - 金额
         * @param {string} currency - 货币
         * @returns {string} 格式化后的价格字符串
         */
        formatPrice(amount, currency = this.baseCurrency) {
            if (!amount || isNaN(amount)) return '';

            const symbols = {
                'CNY': '¥',
                'USD': '$',
                'SGD': 'S$',
                'MYR': 'RM'
            };

            const symbol = symbols[currency] || currency;
            return `${symbol}${amount.toFixed(2)}`;
        }

        /**
         * 更新汇率
         * @param {string} currency - 货币代码
         * @param {number} rate - 汇率
         */
        updateExchangeRate(currency, rate) {
            if (!currency || !rate || isNaN(rate) || rate <= 0) {
                throw new Error('无效的货币或汇率');
            }

            this.defaultRates[currency] = rate;
            this.saveExchangeRates();

            const logger = window.getLogger?.();
            if (logger) {
                logger.log(`汇率已更新: ${currency} = ${rate}`, 'info', {
                    currency,
                    rate,
                    type: 'exchange_rate_update'
                });
            }
        }

        /**
         * 获取支持的货币列表
         * @returns {Array} 货币列表
         */
        getSupportedCurrencies() {
            return Object.keys(this.defaultRates);
        }

        /**
         * 获取当前汇率
         * @returns {Object} 汇率对象
         */
        getCurrentRates() {
            return { ...this.defaultRates };
        }

        /**
         * 重置为默认汇率
         */
        resetToDefaultRates() {
            this.defaultRates = {
                'CNY': 0.615,
                'USD': 4.3,
                'SGD': 3.4,
                'MYR': 1.0
            };
            this.saveExchangeRates();
        }
    }

    // ========================================
    // 第二部分：航班信息服务 (flight-info-service.js)
    // ========================================

    /**
     * 航班信息服务类
     */
    class FlightInfoService {
        constructor() {
            this.baseUrl = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1' 
                ? 'http://localhost:3000' 
                : '';
            this.logger = window.getLogger?.() || console;
            this.initialized = false;
            this.init();
        }

        /**
         * 初始化服务
         */
        init() {
            try {
                this.initialized = true;
                this.logger.log('航班信息服务已初始化', 'info', {
                    baseUrl: this.baseUrl,
                    timestamp: new Date().toISOString()
                });
            } catch (error) {
                this.logger.log('航班信息服务初始化失败', 'error', { error: error.message });
            }
        }

        /**
         * 查询航班信息
         * @param {string} flightNumber - 航班号
         * @param {string} date - 日期 (YYYY-MM-DD)
         * @returns {Promise<Object>} 航班信息
         */
        async getFlightInfo(flightNumber, date) {
            try {
                if (!flightNumber || !date) {
                    throw new Error('航班号和日期不能为空');
                }

                const response = await fetch(`${this.baseUrl}/.netlify/functions/flight-info`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        flightNumber: flightNumber.trim().toUpperCase(),
                        date: date
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const result = await response.json();
                
                this.logger.log('航班信息查询成功', 'success', {
                    flightNumber,
                    date,
                    result
                });

                return result;
            } catch (error) {
                this.logger.log('航班信息查询失败', 'error', {
                    flightNumber,
                    date,
                    error: error.message
                });
                throw error;
            }
        }

        /**
         * 验证航班号格式
         * @param {string} flightNumber - 航班号
         * @returns {boolean} 是否有效
         */
        validateFlightNumber(flightNumber) {
            if (!flightNumber || typeof flightNumber !== 'string') return false;
            
            // 基本的航班号格式验证（2-3个字母 + 数字）
            const flightPattern = /^[A-Z]{2,3}\d{1,4}$/i;
            return flightPattern.test(flightNumber.trim());
        }

        /**
         * 解析航班信息文本
         * @param {string} text - 包含航班信息的文本
         * @returns {Array} 解析出的航班信息
         */
        parseFlightInfoFromText(text) {
            if (!text) return [];

            const flightPattern = /([A-Z]{2,3}\s*\d{1,4})/gi;
            const matches = text.match(flightPattern) || [];
            
            return matches.map(match => ({
                flightNumber: match.replace(/\s+/g, '').toUpperCase(),
                originalText: match
            }));
        }
    }

    // ========================================
    // 第三部分：地址翻译服务 (address-translation-service.js)
    // ========================================

    /**
     * 地址翻译服务类
     * 基于hotel-name-database提供中文地址到英文的翻译功能
     */
    class AddressTranslationService {
        constructor() {
            this.logger = window.getLogger?.() || console;
            this.logger.log('地址翻译服务初始化', 'info');
        }

        /**
         * 检测字符串是否包含中文字符
         * @param {string} text - 要检测的文本
         * @returns {boolean} 是否包含中文
         */
        containsChinese(text) {
            if (!text || typeof text !== 'string') return false;
            const chineseRegex = /[\u4e00-\u9fff\u3400-\u4dbf\uf900-\ufaff]/;
            return chineseRegex.test(text);
        }

        /**
         * 基于hotel-name-database翻译中文地址
         * @param {string} chineseAddress - 中文地址
         * @returns {Object} 翻译结果
         */
        translateAddress(chineseAddress) {
            if (!chineseAddress || !this.containsChinese(chineseAddress)) {
                return {
                    success: false,
                    originalAddress: chineseAddress,
                    translatedAddress: chineseAddress,
                    confidence: 0,
                    method: 'no_translation_needed'
                };
            }

            try {
                // 获取酒店数据库
                const hotelDatabase = window.OTA?.hotelDatabase || window.hotelDatabase;
                if (!hotelDatabase) {
                    return {
                        success: false,
                        originalAddress: chineseAddress,
                        translatedAddress: chineseAddress,
                        confidence: 0,
                        method: 'database_unavailable',
                        error: '酒店数据库不可用'
                    };
                }

                // 在数据库中搜索匹配的中文名称
                const searchResult = this.searchInHotelDatabase(chineseAddress, hotelDatabase);
                
                if (searchResult.found) {
                    return {
                        success: true,
                        originalAddress: chineseAddress,
                        translatedAddress: searchResult.englishName,
                        confidence: searchResult.confidence,
                        method: 'database_match',
                        matchedEntry: searchResult.entry
                    };
                }

                // 如果没有找到精确匹配，尝试部分匹配
                const partialResult = this.partialMatchInDatabase(chineseAddress, hotelDatabase);
                
                if (partialResult.found) {
                    return {
                        success: true,
                        originalAddress: chineseAddress,
                        translatedAddress: partialResult.englishName,
                        confidence: partialResult.confidence,
                        method: 'partial_match',
                        matchedEntry: partialResult.entry,
                        note: '部分匹配结果'
                    };
                }

                // 如果都没有找到，返回原文
                return {
                    success: false,
                    originalAddress: chineseAddress,
                    translatedAddress: chineseAddress,
                    confidence: 0,
                    method: 'no_match_found'
                };

            } catch (error) {
                this.logger.log('地址翻译失败', 'error', { 
                    address: chineseAddress, 
                    error: error.message 
                });
                
                return {
                    success: false,
                    originalAddress: chineseAddress,
                    translatedAddress: chineseAddress,
                    confidence: 0,
                    method: 'translation_error',
                    error: error.message
                };
            }
        }

        /**
         * 在酒店数据库中搜索
         * @param {string} chineseText - 中文文本
         * @param {Object} database - 酒店数据库
         * @returns {Object} 搜索结果
         */
        searchInHotelDatabase(chineseText, database) {
            if (!database || !database.hotels) {
                return { found: false, confidence: 0 };
            }

            const cleanText = chineseText.trim().toLowerCase();
            
            // 精确匹配
            for (const hotel of database.hotels) {
                if (hotel.chinese && hotel.chinese.toLowerCase() === cleanText) {
                    return {
                        found: true,
                        englishName: hotel.english,
                        confidence: 1.0,
                        entry: hotel
                    };
                }
            }

            return { found: false, confidence: 0 };
        }

        /**
         * 部分匹配搜索
         * @param {string} chineseText - 中文文本
         * @param {Object} database - 酒店数据库
         * @returns {Object} 搜索结果
         */
        partialMatchInDatabase(chineseText, database) {
            if (!database || !database.hotels) {
                return { found: false, confidence: 0 };
            }

            const cleanText = chineseText.trim();
            let bestMatch = null;
            let bestConfidence = 0;

            for (const hotel of database.hotels) {
                if (!hotel.chinese) continue;

                // 计算相似度
                const similarity = this.calculateSimilarity(cleanText, hotel.chinese);
                
                if (similarity > bestConfidence && similarity > 0.6) {
                    bestMatch = hotel;
                    bestConfidence = similarity;
                }
            }

            if (bestMatch) {
                return {
                    found: true,
                    englishName: bestMatch.english,
                    confidence: bestConfidence,
                    entry: bestMatch
                };
            }

            return { found: false, confidence: 0 };
        }

        /**
         * 计算字符串相似度
         * @param {string} str1 - 字符串1
         * @param {string} str2 - 字符串2
         * @returns {number} 相似度 (0-1)
         */
        calculateSimilarity(str1, str2) {
            if (!str1 || !str2) return 0;
            if (str1 === str2) return 1;

            const longer = str1.length > str2.length ? str1 : str2;
            const shorter = str1.length > str2.length ? str2 : str1;

            if (longer.length === 0) return 1;

            const editDistance = this.levenshteinDistance(longer, shorter);
            return (longer.length - editDistance) / longer.length;
        }

        /**
         * 计算编辑距离
         * @param {string} str1 - 字符串1
         * @param {string} str2 - 字符串2
         * @returns {number} 编辑距离
         */
        levenshteinDistance(str1, str2) {
            const matrix = [];

            for (let i = 0; i <= str2.length; i++) {
                matrix[i] = [i];
            }

            for (let j = 0; j <= str1.length; j++) {
                matrix[0][j] = j;
            }

            for (let i = 1; i <= str2.length; i++) {
                for (let j = 1; j <= str1.length; j++) {
                    if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
                        matrix[i][j] = matrix[i - 1][j - 1];
                    } else {
                        matrix[i][j] = Math.min(
                            matrix[i - 1][j - 1] + 1,
                            matrix[i][j - 1] + 1,
                            matrix[i - 1][j] + 1
                        );
                    }
                }
            }

            return matrix[str2.length][str1.length];
        }

        /**
         * 批量翻译地址
         * @param {Array} addresses - 地址数组
         * @returns {Array} 翻译结果数组
         */
        batchTranslateAddresses(addresses) {
            if (!Array.isArray(addresses)) return [];

            return addresses.map(address => this.translateAddress(address));
        }
    }

    // ========================================
    // 统一API暴露和服务注册
    // ========================================

    // 创建服务实例
    const currencyConverter = new CurrencyConverter();
    const flightInfoService = new FlightInfoService();
    const addressTranslationService = new AddressTranslationService();

    // 暴露到OTA命名空间
    window.OTA.currencyConverter = currencyConverter;
    window.OTA.flightInfoService = flightInfoService;
    window.OTA.addressTranslationService = addressTranslationService;

    // 向后兼容：暴露到全局window对象
    window.currencyConverter = currencyConverter;
    window.flightInfoService = flightInfoService;
    window.addressTranslationService = addressTranslationService;

    // 工厂函数
    window.getCurrencyConverter = () => currencyConverter;
    window.getFlightInfoService = () => flightInfoService;
    window.getAddressTranslationService = () => addressTranslationService;

    // 暴露到OTA命名空间
    window.OTA.getCurrencyConverter = window.getCurrencyConverter;
    window.OTA.getFlightInfoService = window.getFlightInfoService;
    window.OTA.getAddressTranslationService = window.getAddressTranslationService;

    // 注册到OTA注册中心
    if (window.OTA && window.OTA.Registry) {
        window.OTA.Registry.registerService('currencyConverter', currencyConverter, '@OTA_CURRENCY_CONVERTER');
        window.OTA.Registry.registerService('flightInfoService', flightInfoService, '@OTA_FLIGHT_INFO_SERVICE');
        window.OTA.Registry.registerService('addressTranslationService', addressTranslationService, '@OTA_ADDRESS_TRANSLATION_SERVICE');
        
        window.OTA.Registry.registerFactory('getCurrencyConverter', window.getCurrencyConverter, '@OTA_CURRENCY_CONVERTER_FACTORY');
        window.OTA.Registry.registerFactory('getFlightInfoService', window.getFlightInfoService, '@OTA_FLIGHT_INFO_SERVICE_FACTORY');
        window.OTA.Registry.registerFactory('getAddressTranslationService', window.getAddressTranslationService, '@OTA_ADDRESS_TRANSLATION_SERVICE_FACTORY');
    }

})();
