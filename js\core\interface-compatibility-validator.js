/**
 * 接口兼容性验证器
 * 
 * 设计目标：
 * - 验证OTAManager与现有Manager的接口兼容性
 * - 确保所有必要方法都存在且可用
 * - 验证方法签名和行为一致性
 * - 提供详细的兼容性报告
 * 
 * <AUTHOR>
 * @version 1.0.0
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};
window.OTA.validation = window.OTA.validation || {};

(function() {
    'use strict';

    /**
     * 接口兼容性验证器
     */
    class InterfaceCompatibilityValidator {
        constructor() {
            this.logger = this.getLogger();
            this.validationResults = [];
            this.managerInterfaces = this.defineManagerInterfaces();
            
            this.logger.log('接口兼容性验证器已初始化');
        }

        /**
         * 定义Manager接口规范
         * 基于现有FormManager和EventManager的分析
         * @returns {Object} 接口规范
         */
        defineManagerInterfaces() {
            return {
                // 基础Manager接口
                base: {
                    required: [
                        'init',           // 标准初始化方法
                        'getName',        // 获取管理器名称
                        'isInitialized'   // 检查初始化状态
                    ],
                    optional: [
                        'destroy',        // 销毁方法
                        'getStats',       // 获取统计信息
                        'healthCheck'     // 健康检查
                    ]
                },

                // 日志接口
                logging: {
                    required: [
                        'log'             // 基础日志方法
                    ],
                    optional: [
                        'error',          // 错误日志
                        'warn',           // 警告日志
                        'info',           // 信息日志
                        'debug'           // 调试日志
                    ]
                },

                // 事件接口
                events: {
                    required: [
                        'emit'            // 事件发射
                    ],
                    optional: [
                        'on',             // 事件监听
                        'off'             // 移除监听
                    ]
                },

                // OTAManager特有接口
                ota: {
                    required: [
                        'initialize',     // 原有初始化方法（向后兼容）
                        'registerStrategy',   // 注册策略
                        'switchToStrategy',   // 切换策略
                        'processFormData'     // 处理表单数据
                    ],
                    optional: [
                        'registerDefaultStrategies',  // 注册默认策略
                        'setDefaultStrategy',          // 设置默认策略
                        'updateConfiguration'          // 更新配置
                    ]
                }
            };
        }

        /**
         * 验证OTAManager接口兼容性
         * @param {Object} otaManager - OTAManager实例
         * @returns {Promise<Object>} 验证结果
         */
        async validateOTAManager(otaManager) {
            const validationResult = {
                timestamp: Date.now(),
                managerName: 'OTAManager',
                overall: true,
                interfaces: {},
                issues: [],
                recommendations: []
            };

            try {
                this.logger.log('开始验证OTAManager接口兼容性...');

                // 验证基础接口
                validationResult.interfaces.base = await this.validateInterface(
                    otaManager, 
                    this.managerInterfaces.base, 
                    'base'
                );

                // 验证日志接口
                validationResult.interfaces.logging = await this.validateInterface(
                    otaManager, 
                    this.managerInterfaces.logging, 
                    'logging'
                );

                // 验证事件接口
                validationResult.interfaces.events = await this.validateInterface(
                    otaManager, 
                    this.managerInterfaces.events, 
                    'events'
                );

                // 验证OTA特有接口
                validationResult.interfaces.ota = await this.validateInterface(
                    otaManager, 
                    this.managerInterfaces.ota, 
                    'ota'
                );

                // 计算总体结果
                const interfaceResults = Object.values(validationResult.interfaces);
                validationResult.overall = interfaceResults.every(result => result.passed);

                // 收集问题和建议
                interfaceResults.forEach(result => {
                    validationResult.issues.push(...result.issues);
                    validationResult.recommendations.push(...result.recommendations);
                });

                // 执行功能性验证
                const functionalResult = await this.validateFunctionality(otaManager);
                validationResult.functional = functionalResult;
                
                if (!functionalResult.passed) {
                    validationResult.overall = false;
                    validationResult.issues.push(...functionalResult.issues);
                }

                this.logger.log(`OTAManager接口兼容性验证完成: ${validationResult.overall ? '通过' : '失败'}`);
                
                return validationResult;

            } catch (error) {
                this.logger.log('验证OTAManager接口兼容性时出错', 'error', error);
                validationResult.overall = false;
                validationResult.issues.push(`验证过程出错: ${error.message}`);
                return validationResult;
            }
        }

        /**
         * 验证特定接口
         * @param {Object} manager - Manager实例
         * @param {Object} interfaceSpec - 接口规范
         * @param {string} interfaceName - 接口名称
         * @returns {Promise<Object>} 验证结果
         */
        async validateInterface(manager, interfaceSpec, interfaceName) {
            const result = {
                name: interfaceName,
                passed: true,
                required: { total: 0, passed: 0, failed: [] },
                optional: { total: 0, passed: 0, failed: [] },
                issues: [],
                recommendations: []
            };

            // 验证必需方法
            if (interfaceSpec.required) {
                result.required.total = interfaceSpec.required.length;
                
                for (const methodName of interfaceSpec.required) {
                    if (typeof manager[methodName] === 'function') {
                        result.required.passed++;
                        this.logger.log(`✅ 必需方法存在: ${methodName}`);
                    } else {
                        result.required.failed.push(methodName);
                        result.issues.push(`缺少必需方法: ${methodName}`);
                        result.passed = false;
                        this.logger.log(`❌ 缺少必需方法: ${methodName}`, 'error');
                    }
                }
            }

            // 验证可选方法
            if (interfaceSpec.optional) {
                result.optional.total = interfaceSpec.optional.length;
                
                for (const methodName of interfaceSpec.optional) {
                    if (typeof manager[methodName] === 'function') {
                        result.optional.passed++;
                        this.logger.log(`✅ 可选方法存在: ${methodName}`);
                    } else {
                        result.optional.failed.push(methodName);
                        result.recommendations.push(`建议实现可选方法: ${methodName}`);
                        this.logger.log(`⚠️ 可选方法缺失: ${methodName}`, 'warn');
                    }
                }
            }

            return result;
        }

        /**
         * 验证功能性
         * @param {Object} manager - Manager实例
         * @returns {Promise<Object>} 功能验证结果
         */
        async validateFunctionality(manager) {
            const result = {
                passed: true,
                tests: [],
                issues: []
            };

            try {
                // 测试1: 日志功能
                const logTest = await this.testLogging(manager);
                result.tests.push(logTest);
                if (!logTest.passed) result.passed = false;

                // 测试2: 事件功能
                const eventTest = await this.testEvents(manager);
                result.tests.push(eventTest);
                if (!eventTest.passed) result.passed = false;

                // 测试3: 初始化功能
                const initTest = await this.testInitialization(manager);
                result.tests.push(initTest);
                if (!initTest.passed) result.passed = false;

                // 测试4: OTA特有功能
                const otaTest = await this.testOTAFunctionality(manager);
                result.tests.push(otaTest);
                if (!otaTest.passed) result.passed = false;

                // 收集所有问题
                result.tests.forEach(test => {
                    if (test.issues) {
                        result.issues.push(...test.issues);
                    }
                });

            } catch (error) {
                result.passed = false;
                result.issues.push(`功能验证出错: ${error.message}`);
            }

            return result;
        }

        /**
         * 测试日志功能
         * @param {Object} manager - Manager实例
         * @returns {Promise<Object>} 测试结果
         */
        async testLogging(manager) {
            const test = {
                name: 'logging',
                passed: true,
                issues: []
            };

            try {
                // 测试基础log方法
                if (typeof manager.log === 'function') {
                    manager.log('Test log message');
                    this.logger.log('✅ 日志功能测试通过');
                } else {
                    test.passed = false;
                    test.issues.push('log方法不可用');
                }

                // 测试error方法（如果存在）
                if (typeof manager.error === 'function') {
                    manager.error('Test error message');
                    this.logger.log('✅ 错误日志功能测试通过');
                }

            } catch (error) {
                test.passed = false;
                test.issues.push(`日志功能测试失败: ${error.message}`);
            }

            return test;
        }

        /**
         * 测试事件功能
         * @param {Object} manager - Manager实例
         * @returns {Promise<Object>} 测试结果
         */
        async testEvents(manager) {
            const test = {
                name: 'events',
                passed: true,
                issues: []
            };

            try {
                // 测试emit方法
                if (typeof manager.emit === 'function') {
                    manager.emit('test-event', { test: true });
                    this.logger.log('✅ 事件发射功能测试通过');
                } else {
                    test.passed = false;
                    test.issues.push('emit方法不可用');
                }

            } catch (error) {
                test.passed = false;
                test.issues.push(`事件功能测试失败: ${error.message}`);
            }

            return test;
        }

        /**
         * 测试初始化功能
         * @param {Object} manager - Manager实例
         * @returns {Promise<Object>} 测试结果
         */
        async testInitialization(manager) {
            const test = {
                name: 'initialization',
                passed: true,
                issues: []
            };

            try {
                // 测试init方法
                if (typeof manager.init === 'function') {
                    // 如果未初始化，尝试初始化
                    if (!manager.isInitialized || !manager.isInitialized()) {
                        await manager.init();
                    }
                    this.logger.log('✅ 初始化功能测试通过');
                } else {
                    test.passed = false;
                    test.issues.push('init方法不可用');
                }

                // 测试initialize方法（向后兼容）
                if (typeof manager.initialize === 'function') {
                    this.logger.log('✅ initialize方法存在（向后兼容）');
                }

            } catch (error) {
                test.passed = false;
                test.issues.push(`初始化功能测试失败: ${error.message}`);
            }

            return test;
        }

        /**
         * 测试OTA特有功能
         * @param {Object} manager - Manager实例
         * @returns {Promise<Object>} 测试结果
         */
        async testOTAFunctionality(manager) {
            const test = {
                name: 'ota-specific',
                passed: true,
                issues: []
            };

            try {
                // 测试策略注册
                if (typeof manager.registerStrategy === 'function') {
                    manager.registerStrategy('test', { name: 'TestStrategy' });
                    this.logger.log('✅ 策略注册功能测试通过');
                } else {
                    test.passed = false;
                    test.issues.push('registerStrategy方法不可用');
                }

                // 测试策略切换
                if (typeof manager.switchToStrategy === 'function') {
                    manager.switchToStrategy('test');
                    this.logger.log('✅ 策略切换功能测试通过');
                } else {
                    test.passed = false;
                    test.issues.push('switchToStrategy方法不可用');
                }

            } catch (error) {
                test.passed = false;
                test.issues.push(`OTA功能测试失败: ${error.message}`);
            }

            return test;
        }

        /**
         * 执行完整的兼容性验证
         * @returns {Promise<Object>} 完整验证结果
         */
        async runFullValidation() {
            const fullResult = {
                timestamp: Date.now(),
                overall: true,
                managers: {},
                summary: {
                    total: 0,
                    passed: 0,
                    failed: 0
                }
            };

            try {
                this.logger.log('开始执行完整的接口兼容性验证...');

                // 验证OTAManager
                if (window.OTA && window.OTA.otaManager) {
                    const otaResult = await this.validateOTAManager(window.OTA.otaManager);
                    fullResult.managers.otaManager = otaResult;
                    fullResult.summary.total++;
                    
                    if (otaResult.overall) {
                        fullResult.summary.passed++;
                    } else {
                        fullResult.summary.failed++;
                        fullResult.overall = false;
                    }
                } else {
                    fullResult.overall = false;
                    fullResult.managers.otaManager = {
                        error: 'OTAManager not found'
                    };
                    fullResult.summary.total++;
                    fullResult.summary.failed++;
                }

                this.logger.log(`完整验证完成: ${fullResult.summary.passed}/${fullResult.summary.total} 通过`);
                
                return fullResult;

            } catch (error) {
                this.logger.log('执行完整验证时出错', 'error', error);
                fullResult.overall = false;
                fullResult.error = error.message;
                return fullResult;
            }
        }

        /**
         * 获取Logger实例
         * @returns {Object} Logger实例
         */
        getLogger() {
            try {
                if (typeof getLogger === 'function') {
                    return getLogger();
                }
                return {
                    log: (message, level = 'info', data = null) => {
                        console.log(`[InterfaceValidator][${level.toUpperCase()}] ${message}`, data || '');
                    }
                };
            } catch (error) {
                return {
                    log: (message, level = 'info', data = null) => {
                        console.log(`[InterfaceValidator][${level.toUpperCase()}] ${message}`, data || '');
                    }
                };
            }
        }
    }

    // 创建全局验证器实例
    const interfaceValidator = new InterfaceCompatibilityValidator();

    // 暴露到OTA命名空间
    window.OTA.validation.InterfaceCompatibilityValidator = InterfaceCompatibilityValidator;
    window.OTA.interfaceValidator = interfaceValidator;

    // 提供便捷的验证函数
    window.OTA.validateInterfaces = function() {
        return interfaceValidator.runFullValidation();
    };

    try {
        const enabled = window.OTA?.featureToggle?.isEnabled('enableInterfaceValidation');
        if (enabled) {
            console.log('✅ 接口兼容性验证器已加载并可用');
        } else {
            console.log('🧪 接口兼容性验证器已加载（未启用，受特性开关控制）');
        }
    } catch (_) { /* 忽略 */ }

})();
