// Script Manifest - authoritative list of scripts and phases
// Keep order strictly as required by architecture. See .github/copilot-instructions.md

window.OTA = window.OTA || {};

(function() {
  'use strict';

  // Simple environment switch: use window.OTA.env or URL ?dev=1 to include dev tools
  const isDev = (() => {
    try {
      const fromOTA = window.OTA && window.OTA.env && (window.OTA.env === 'dev' || window.OTA.env === 'development');
      const urlDev = /[?&]dev=1(?:&|$)/.test(location.search);
      return !!(fromOTA || urlDev);
    } catch (_) {
      return false;
    }
  })();

  // Define phases with ordered scripts. Avoid external origins (CSP)
  const phases = [
    { name: 'core', scripts: [
      'js/core/dependency-container.js',
      'js/core/service-locator.js',
      'js/core/ota-registry.js',
      'js/core/application-bootstrap.js',
      'js/core/global-event-coordinator.js',
      'js/core/component-lifecycle-manager.js',
      'js/core/unified-data-manager.js',
      'js/core/vehicle-configuration-manager.js',
      'js/core/vehicle-config-integration.js',
      'js/core/global-field-standardization-layer.js',
      'js/core/language-detector.js',
      'js/core/base-ota-strategy.js',
      'js/core/ota-configuration-manager.js',
      'js/core/ota-event-bridge.js',
      'js/core/ota-system-integrator.js',
      'js/adapters/base-manager-adapter.js',
      'js/adapters/ota-manager-decorator.js',
      'js/core/ota-manager-factory.js',
      'js/core/feature-toggle.js',
      'js/core/ota-bootstrap-integration.js',
    ] },
    { name: 'base-utils', scripts: [
      'js/utils.js',
      'js/logger.js',
      'js/ota-channel-mapping.js',
      'js/hotel-name-database.js'
    ] },
    { name: 'ota-system', scripts: [
      'js/ota-system/ota-system-loader.js',
      'js/ota-system/config/prompt-templates.js',
      'js/ota-system/ota-channel-detector.js',
      'js/ota-system/ota-customization-engine.js',
      'js/ota-system/integrations/gemini-integration.js',
      'js/ota-system/integrations/multi-order-integration.js'
    ] },
    { name: 'strategies', scripts: [
      'js/strategies/fliggy-ota-strategy.js',
      'js/strategies/jingge-ota-strategy.js'
    ] },
    { name: 'services', scripts: [
      'js/app-state.js',
      'js/language-manager.js',
      'js/api-service.js',
      'js/hotel-data-inline.js',
      'js/gemini-service.js',
      'js/order-history-manager.js',
      'js/image-upload-manager.js',
  'js/currency-converter.js',
  'js/flight-info-service.js',
  'js/address-translation-service.js'
    ] },
    { name: 'multi-order', scripts: [
      'js/multi-order/field-mapping-config.js',
      'js/multi-order/field-mapping-validator.js',
      'js/multi-order/multi-order-detector.js',
      'js/multi-order/multi-order-renderer.js',
      'js/multi-order/multi-order-processor.js',
      'js/multi-order/multi-order-transformer.js',
      'js/multi-order/field-mapping-tests.js',
      'js/multi-order/multi-order-state-manager.js',
      'js/multi-order/batch-processor.js',
      'js/multi-order/multi-order-coordinator.js',
      'js/multi-order-manager-v2.js',
      'js/multi-order/system-integrity-checker.js'
    ] },
    { name: 'ui-deps', scripts: [
      'js/paging-service-manager.js',
      'js/grid-resizer.js',
      'js/i18n.js',
      'js/auto-resize-manager.js',
      'js/managers/form-manager.js',
      'js/managers/price-manager.js',
      'js/managers/event-manager.js',
      'js/managers/ui-state-manager.js',
  'js/managers/ota-manager.js',
  'js/managers/realtime-analysis-manager.js'
    ] },
    { name: 'ui', scripts: [
      'js/ui-manager.js',
      'main.js'
    ] }
  ];

  window.OTA.scriptManifest = {
    phases,
    version: '1.0',
    createdAt: new Date().toISOString()
  };

  console.log('✅ Script manifest ready');
})();
