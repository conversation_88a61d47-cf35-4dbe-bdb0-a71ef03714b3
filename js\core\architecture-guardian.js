/**
 * @OTA_CORE 架构违规警告系统
 * 🏷️ 标签: @OTA_ARCHITECTURE_GUARDIAN
 * 📝 功能: 实时监控架构违规，防止代码质量退化
 * ⚠️ 警告: 已注册，请勿重复开发
 * 
 * 防护机制:
 * - 实时监控全局变量污染
 * - 检测未注册的OTA函数
 * - 监控文件大小和复杂度
 * - 警告违反架构原则的代码
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};

(function() {
    'use strict';

    /**
     * @OTA_CORE 架构守护者
     * 实时监控和警告架构违规行为
     */
    const ArchitectureGuardian = {
        // 监控配置
        config: {
            // 全局变量监控
            globalVarLimit: 50,           // 全局变量数量限制
            otaVarLimit: 30,              // OTA命名空间变量限制
            
            // 函数监控  
            maxFunctionLength: 100,       // 单个函数最大行数
            maxFileSize: 1000,            // 单个文件最大行数（估算）
            
            // 性能监控
            maxMemoryUsage: 50 * 1024 * 1024, // 50MB内存限制
            maxResponseTime: 3000,         // 3秒响应时间限制
            
            // 检查间隔
            monitoringInterval: 60000,     // 60秒检查一次
            warningThreshold: 3,           // 3次违规后发出警告
            criticalThreshold: 5           // 5次违规后标记为严重
        },
        
        // 违规记录
        violations: [],
        warningHistory: [],
        
        // 监控状态
        isMonitoring: false,
        monitoringTimer: null,
        
        // 基线数据（用于比较变化）
        baseline: {
            globalVarCount: 0,
            otaVarCount: 0,
            memoryUsage: 0,
            timestamp: null
        },
        
        /**
         * 初始化架构守护者
         */
        init() {
            this.establishBaseline();
            this.setupGlobalMonitoring();
            this.startMonitoring();
            
            const logger = window.getLogger ? window.getLogger() : console;
            if (logger && logger.log) {
                logger.log('🛡️ 架构守护者已启动', 'info', {
                    config: this.config,
                    baseline: this.baseline
                });
            }
        },
        
        /**
         * 建立基线数据
         */
        establishBaseline() {
            this.baseline = {
                globalVarCount: Object.keys(window).length,
                otaVarCount: window.OTA ? Object.keys(window.OTA).length : 0,
                memoryUsage: this.getMemoryUsage(),
                timestamp: new Date().toISOString()
            };
        },
        
        /**
         * 设置全局监控
         */
        setupGlobalMonitoring() {
            // 监控新的全局变量
            const originalWindow = { ...window };
            
            // 使用Proxy监控window对象变化（如果支持）
            if (typeof Proxy !== 'undefined') {
                this.setupProxyMonitoring();
            }
            
            // 监控未捕获的错误
            window.addEventListener('error', (event) => {
                this.recordViolation('UNCAUGHT_ERROR', {
                    message: event.message,
                    filename: event.filename,
                    lineno: event.lineno,
                    colno: event.colno
                }, 'critical');
            });
            
            // 监控Promise拒绝
            window.addEventListener('unhandledrejection', (event) => {
                this.recordViolation('UNHANDLED_PROMISE_REJECTION', {
                    reason: event.reason
                }, 'high');
            });
        },
        
        /**
         * 设置Proxy监控（如果浏览器支持）
         */
        setupProxyMonitoring() {
            try {
                // 注意：这是一个实验性功能，可能影响性能
                const guardian = this;
                
                // 监控OTA命名空间的变化
                if (window.OTA && typeof window.OTA === 'object') {
                    const originalOTA = window.OTA;
                    window.OTA = new Proxy(originalOTA, {
                        set(target, property, value) {
                            // 检查是否为新的OTA属性
                            if (!(property in target)) {
                                guardian.checkOTAPropertyAddition(property, value);
                            }
                            
                            return Reflect.set(target, property, value);
                        }
                    });
                }
            } catch (error) {
                // Proxy不支持或设置失败，使用传统监控方式
                console.warn('Proxy监控设置失败，使用传统监控方式', error);
            }
        },
        
        /**
         * 检查OTA属性添加
         */
        checkOTAPropertyAddition(property, value) {
            // 检查是否有适当的标签
            if (typeof value === 'function') {
                const funcStr = value.toString();
                const hasOTATag = /@OTA_\w+/.test(funcStr);
                
                if (!hasOTATag) {
                    this.recordViolation('UNTAGGED_OTA_FUNCTION', {
                        property,
                        functionName: value.name || 'anonymous',
                        recommendation: `为 ${property} 添加适当的@OTA_标签`
                    }, 'medium');
                }
            }
            
            // 检查命名规范
            if (!/^[a-zA-Z][a-zA-Z0-9]*$/.test(property)) {
                this.recordViolation('INVALID_PROPERTY_NAME', {
                    property,
                    recommendation: '属性名应使用驼峰命名法'
                }, 'low');
            }
        },
        
        /**
         * 开始监控
         */
        startMonitoring() {
            if (this.isMonitoring) return;
            
            this.isMonitoring = true;
            this.monitoringTimer = setInterval(() => {
                this.performSystemCheck();
            }, this.config.monitoringInterval);
            
            console.log('🔍 架构监控已启动，检查间隔:', this.config.monitoringInterval / 1000, '秒');
        },
        
        /**
         * 停止监控
         */
        stopMonitoring() {
            if (this.monitoringTimer) {
                clearInterval(this.monitoringTimer);
                this.monitoringTimer = null;
            }
            this.isMonitoring = false;
            console.log('⏹️ 架构监控已停止');
        },
        
        /**
         * 执行系统检查
         */
        performSystemCheck() {
            const results = {
                timestamp: new Date().toISOString(),
                checks: {}
            };
            
            // 全局变量检查
            results.checks.globalVars = this.checkGlobalVariables();
            
            // OTA变量检查  
            results.checks.otaVars = this.checkOTAVariables();
            
            // 内存使用检查
            results.checks.memory = this.checkMemoryUsage();
            
            // 性能检查
            results.checks.performance = this.checkPerformance();
            
            // 注册检查
            results.checks.registry = this.checkRegistryCompliance();
            
            // 生成警告
            this.processCheckResults(results);
            
            return results;
        },
        
        /**
         * 检查全局变量
         */
        checkGlobalVariables() {
            const currentCount = Object.keys(window).length;
            const increase = currentCount - this.baseline.globalVarCount;
            
            const result = {
                currentCount,
                baselineCount: this.baseline.globalVarCount,
                increase,
                status: increase > 10 ? 'warning' : increase > 20 ? 'critical' : 'ok'
            };
            
            if (increase > 10) {
                this.recordViolation('GLOBAL_VARIABLE_INCREASE', {
                    increase,
                    currentCount,
                    recommendation: '考虑将新的全局变量注册到OTA命名空间'
                }, increase > 20 ? 'critical' : 'medium');
            }
            
            return result;
        },
        
        /**
         * 检查OTA变量
         */
        checkOTAVariables() {
            if (!window.OTA) {
                return { status: 'error', message: 'OTA命名空间不存在' };
            }
            
            const currentCount = Object.keys(window.OTA).length;
            const increase = currentCount - this.baseline.otaVarCount;
            
            const result = {
                currentCount,
                baselineCount: this.baseline.otaVarCount,
                increase,
                status: currentCount > this.config.otaVarLimit ? 'warning' : 'ok'
            };
            
            if (currentCount > this.config.otaVarLimit) {
                this.recordViolation('OTA_NAMESPACE_BLOAT', {
                    currentCount,
                    limit: this.config.otaVarLimit,
                    recommendation: '考虑重构以减少OTA命名空间的变量数量'
                }, 'medium');
            }
            
            return result;
        },
        
        /**
         * 检查内存使用
         */
        checkMemoryUsage() {
            const currentUsage = this.getMemoryUsage();
            const result = {
                currentUsage,
                limit: this.config.maxMemoryUsage,
                status: currentUsage > this.config.maxMemoryUsage ? 'critical' : 'ok'
            };
            
            if (currentUsage > this.config.maxMemoryUsage) {
                this.recordViolation('MEMORY_USAGE_HIGH', {
                    currentUsage,
                    limit: this.config.maxMemoryUsage,
                    recommendation: '检查是否有内存泄漏或优化内存使用'
                }, 'critical');
            }
            
            return result;
        },
        
        /**
         * 检查性能
         */
        checkPerformance() {
            // 简单的性能检查
            const start = performance.now();
            
            // 模拟一些操作
            for (let i = 0; i < 1000; i++) {
                Math.random();
            }
            
            const responseTime = performance.now() - start;
            const result = {
                responseTime,
                limit: this.config.maxResponseTime,
                status: responseTime > this.config.maxResponseTime ? 'warning' : 'ok'
            };
            
            return result;
        },
        
        /**
         * 检查注册合规性
         */
        checkRegistryCompliance() {
            if (!window.OTA.Registry) {
                return { status: 'error', message: 'OTA.Registry不存在' };
            }
            
            const registryInfo = window.OTA.Registry.getRegistryInfo();
            const result = {
                registered: registryInfo.totalRegistered,
                duplicates: registryInfo.totalDuplicates,
                status: registryInfo.totalDuplicates > 0 ? 'warning' : 'ok'
            };
            
            if (registryInfo.totalDuplicates > 0) {
                this.recordViolation('REGISTRY_DUPLICATES', {
                    duplicateCount: registryInfo.totalDuplicates,
                    recommendation: '运行 detectDuplicates() 并清理重复定义'
                }, 'high');
            }
            
            return result;
        },
        
        /**
         * 记录违规
         */
        recordViolation(type, details, severity = 'medium') {
            const violation = {
                type,
                details,
                severity,
                timestamp: new Date().toISOString(),
                id: `${type}_${Date.now()}`
            };
            
            this.violations.push(violation);
            
            // 限制违规记录数量
            if (this.violations.length > 100) {
                this.violations = this.violations.slice(-50);
            }
            
            // 根据严重性发出警告
            if (severity === 'critical') {
                console.error('🚨 严重架构违规:', violation);
            } else if (severity === 'high') {
                console.warn('⚠️ 高级架构违规:', violation);
            }
            
            return violation;
        },
        
        /**
         * 处理检查结果
         */
        processCheckResults(results) {
            const warningTypes = [];
            
            Object.entries(results.checks).forEach(([checkType, result]) => {
                if (result.status === 'warning' || result.status === 'critical') {
                    warningTypes.push(checkType);
                }
            });
            
            if (warningTypes.length > 0) {
                const warning = {
                    timestamp: results.timestamp,
                    types: warningTypes,
                    severity: warningTypes.some(t => results.checks[t].status === 'critical') ? 'critical' : 'warning'
                };
                
                this.warningHistory.push(warning);
                
                // 生成警告报告
                if (this.warningHistory.length % this.config.warningThreshold === 0) {
                    this.generateWarningReport();
                }
            }
        },
        
        /**
         * 生成警告报告
         */
        generateWarningReport() {
            const recentWarnings = this.warningHistory.slice(-10);
            const report = {
                timestamp: new Date().toISOString(),
                summary: {
                    totalWarnings: this.warningHistory.length,
                    recentWarnings: recentWarnings.length,
                    criticalCount: recentWarnings.filter(w => w.severity === 'critical').length
                },
                recommendations: this.generateRecommendations(),
                recentViolations: this.violations.slice(-20)
            };
            
            console.group('🛡️ 架构守护者报告');
            console.table(report.summary);
            console.log('💡 建议:', report.recommendations);
            console.groupEnd();
            
            return report;
        },
        
        /**
         * 生成建议
         */
        generateRecommendations() {
            const recommendations = [];
            
            const violationTypes = [...new Set(this.violations.map(v => v.type))];
            
            if (violationTypes.includes('GLOBAL_VARIABLE_INCREASE')) {
                recommendations.push('考虑使用OTA命名空间而不是全局变量');
            }
            
            if (violationTypes.includes('UNTAGGED_OTA_FUNCTION')) {
                recommendations.push('为所有OTA函数添加适当的@OTA_标签');
            }
            
            if (violationTypes.includes('REGISTRY_DUPLICATES')) {
                recommendations.push('运行重复检测工具并清理重复定义');
            }
            
            if (violationTypes.includes('MEMORY_USAGE_HIGH')) {
                recommendations.push('检查内存泄漏并优化内存使用');
            }
            
            return recommendations;
        },
        
        /**
         * 获取内存使用情况
         */
        getMemoryUsage() {
            if (performance.memory) {
                return performance.memory.usedJSHeapSize;
            }
            return 0; // 无法获取内存信息时返回0
        },
        
        /**
         * 生成完整报告
         */
        generateFullReport() {
            const systemCheck = this.performSystemCheck();
            const report = {
                timestamp: new Date().toISOString(),
                config: this.config,
                baseline: this.baseline,
                currentStatus: systemCheck,
                violationsSummary: {
                    total: this.violations.length,
                    bySeverity: this.groupViolationsBySeverity(),
                    byType: this.groupViolationsByType()
                },
                recommendations: this.generateRecommendations(),
                healthScore: this.calculateHealthScore()
            };
            
            return report;
        },
        
        /**
         * 按严重性分组违规
         */
        groupViolationsBySeverity() {
            return this.violations.reduce((acc, violation) => {
                acc[violation.severity] = (acc[violation.severity] || 0) + 1;
                return acc;
            }, {});
        },
        
        /**
         * 按类型分组违规
         */
        groupViolationsByType() {
            return this.violations.reduce((acc, violation) => {
                acc[violation.type] = (acc[violation.type] || 0) + 1;
                return acc;
            }, {});
        },
        
        /**
         * 计算健康评分
         */
        calculateHealthScore() {
            const severityWeights = { low: 1, medium: 3, high: 5, critical: 10 };
            const penalty = this.violations.reduce((total, violation) => {
                return total + (severityWeights[violation.severity] || 3);
            }, 0);
            
            const baseScore = 100;
            const score = Math.max(0, baseScore - penalty);
            const grade = score >= 90 ? 'A' : score >= 80 ? 'B' : score >= 70 ? 'C' : score >= 60 ? 'D' : 'F';
            
            return {
                score,
                grade,
                status: score >= 80 ? 'healthy' : score >= 60 ? 'warning' : 'critical',
                totalPenalty: penalty
            };
        }
    };

    // 注册到OTA命名空间
    window.OTA.ArchitectureGuardian = ArchitectureGuardian;
    
    // 全局访问（向后兼容）
    window.ArchitectureGuardian = ArchitectureGuardian;
    
    // 注册到OTA注册中心
    if (window.OTA && window.OTA.Registry) {
        window.OTA.Registry.registerUtil('ArchitectureGuardian', ArchitectureGuardian, '@OTA_ARCHITECTURE_GUARDIAN');
    }
    
    // 导出全局调试命令
    window.architectureReport = () => {
        const report = ArchitectureGuardian.generateFullReport();
        console.group('🛡️ 架构守护者完整报告');
        console.log('📊 健康评分:', report.healthScore);
        console.table(report.violationsSummary);
        console.log('💡 建议:', report.recommendations);
        console.groupEnd();
        return report;
    };
    
    window.startArchitectureGuardian = () => {
        ArchitectureGuardian.init();
        return '🛡️ 架构守护者已启动';
    };
    
    window.stopArchitectureGuardian = () => {
        ArchitectureGuardian.stopMonitoring();
        return '⏹️ 架构守护者已停止';
    };
    
    // 减法开发：默认不自动启动，需通过特性开关 enablePerformanceMonitoring 启用
    if (typeof window !== 'undefined') {
        setTimeout(() => {
            try {
                const enabled = window.OTA?.featureToggle?.isEnabled('enablePerformanceMonitoring');
                if (enabled) {
                    ArchitectureGuardian.init();
                } else {
                    console.log('🛡️ 架构守护者已加载（未启用，受特性开关控制）');
                }
            } catch (e) {
                // 静默失败，避免影响主流程
            }
        }, 5000);
    }

})();