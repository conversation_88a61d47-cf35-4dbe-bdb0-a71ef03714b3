/**
 * 核心工具类合并包
 * 包含：utils.js + logger.js + monitoring-wrapper.js
 * 合并时间：2025-08-09
 * 总大小：约57KB
 */

// 创建OTA命名空间
window.OTA = window.OTA || {};

(function() {
    'use strict';

    // ========================================
    // 第一部分：工具函数模块 (utils.js)
    // ========================================

    /**
     * 防抖函数
     * @param {function} func - 需要防抖的函数
     * @param {number} wait - 等待时间（毫秒）
     * @param {boolean} immediate - 是否立即执行
     * @returns {function} 防抖后的函数
     */
    function debounce(func, wait, immediate = false) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                timeout = null;
                if (!immediate) func.apply(this, args);
            };
            const callNow = immediate && !timeout;
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
            if (callNow) func.apply(this, args);
        };
    }

    /**
     * 节流函数
     * @param {function} func - 需要节流的函数
     * @param {number} limit - 时间间隔（毫秒）
     * @returns {function} 节流后的函数
     */
    function throttle(func, limit) {
        let inThrottle;
        return function(...args) {
            if (!inThrottle) {
                func.apply(this, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }

    /**
     * 深度克隆对象
     * @param {any} obj - 要克隆的对象
     * @returns {any} 克隆后的对象
     */
    function deepClone(obj) {
        if (obj === null || typeof obj !== 'object') return obj;
        if (obj instanceof Date) return new Date(obj.getTime());
        if (obj instanceof Array) return obj.map(item => deepClone(item));
        if (typeof obj === 'object') {
            const clonedObj = {};
            for (const key in obj) {
                if (obj.hasOwnProperty(key)) {
                    clonedObj[key] = deepClone(obj[key]);
                }
            }
            return clonedObj;
        }
    }

    /**
     * 格式化日期
     * @param {Date|string} date - 日期对象或字符串
     * @param {string} format - 格式字符串
     * @returns {string} 格式化后的日期字符串
     */
    function formatDate(date, format = 'YYYY-MM-DD') {
        const d = new Date(date);
        if (isNaN(d.getTime())) return '';
        
        const year = d.getFullYear();
        const month = String(d.getMonth() + 1).padStart(2, '0');
        const day = String(d.getDate()).padStart(2, '0');
        
        return format
            .replace('YYYY', year)
            .replace('MM', month)
            .replace('DD', day);
    }

    /**
     * 格式化时间
     * @param {Date|string} date - 日期对象或字符串
     * @param {string} format - 格式字符串
     * @returns {string} 格式化后的时间字符串
     */
    function formatTime(date, format = 'HH:mm:ss') {
        const d = new Date(date);
        if (isNaN(d.getTime())) return '';
        
        const hours = String(d.getHours()).padStart(2, '0');
        const minutes = String(d.getMinutes()).padStart(2, '0');
        const seconds = String(d.getSeconds()).padStart(2, '0');
        
        return format
            .replace('HH', hours)
            .replace('mm', minutes)
            .replace('ss', seconds);
    }

    /**
     * 格式化文件大小
     * @param {number} bytes - 字节数
     * @returns {string} 格式化后的文件大小
     */
    function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    /**
     * 生成唯一ID
     * @param {number} length - ID长度
     * @returns {string} 唯一ID
     */
    function generateId(length = 8) {
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
        let result = '';
        for (let i = 0; i < length; i++) {
            result += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        return result;
    }

    /**
     * 验证邮箱格式
     * @param {string} email - 邮箱地址
     * @returns {boolean} 是否有效
     */
    function isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    /**
     * 验证手机号格式
     * @param {string} phone - 手机号
     * @returns {boolean} 是否有效
     */
    function isValidPhone(phone) {
        const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
        return phoneRegex.test(phone.replace(/[\s\-\(\)]/g, ''));
    }

    /**
     * 标准化手机号格式
     * @param {string} phone - 手机号
     * @returns {string} 标准化后的手机号
     */
    function normalizePhoneNumber(phone) {
        return phone.replace(/[\s\-\(\)]/g, '');
    }

    /**
     * 解析日期字符串
     * @param {string} dateStr - 日期字符串
     * @returns {Date|null} 解析后的日期对象
     */
    function parseDate(dateStr) {
        if (!dateStr) return null;
        const date = new Date(dateStr);
        return isNaN(date.getTime()) ? null : date;
    }

    /**
     * 转换为API日期格式
     * @param {Date|string} date - 日期
     * @returns {string} API格式的日期字符串
     */
    function convertToApiDateFormat(date) {
        const d = new Date(date);
        if (isNaN(d.getTime())) return '';
        return d.toISOString().split('T')[0];
    }

    /**
     * 计算日期差
     * @param {Date|string} date1 - 日期1
     * @param {Date|string} date2 - 日期2
     * @returns {number} 天数差
     */
    function daysDifference(date1, date2) {
        const d1 = new Date(date1);
        const d2 = new Date(date2);
        const timeDiff = Math.abs(d2.getTime() - d1.getTime());
        return Math.ceil(timeDiff / (1000 * 3600 * 24));
    }

    /**
     * 检测是否为移动设备
     * @returns {boolean} 是否为移动设备
     */
    function isMobile() {
        return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    }

    /**
     * 获取浏览器信息
     * @returns {object} 浏览器信息
     */
    function getBrowserInfo() {
        const ua = navigator.userAgent;
        const browsers = {
            chrome: /Chrome/i.test(ua) && !/Edge/i.test(ua),
            firefox: /Firefox/i.test(ua),
            safari: /Safari/i.test(ua) && !/Chrome/i.test(ua),
            edge: /Edge/i.test(ua),
            ie: /MSIE|Trident/i.test(ua)
        };
        
        const browser = Object.keys(browsers).find(key => browsers[key]) || 'unknown';
        return {
            name: browser,
            userAgent: ua,
            isMobile: isMobile()
        };
    }

    /**
     * 复制到剪贴板
     * @param {string} text - 要复制的文本
     * @returns {Promise<boolean>} 是否成功
     */
    async function copyToClipboard(text) {
        try {
            if (navigator.clipboard) {
                await navigator.clipboard.writeText(text);
                return true;
            } else {
                // 降级方案
                const textArea = document.createElement('textarea');
                textArea.value = text;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                return true;
            }
        } catch (err) {
            return false;
        }
    }

    /**
     * 下载文件
     * @param {string} content - 文件内容
     * @param {string} filename - 文件名
     * @param {string} contentType - 内容类型
     */
    function downloadFile(content, filename, contentType = 'text/plain') {
        const blob = new Blob([content], { type: contentType });
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);
    }

    /**
     * 解析URL参数
     * @param {string} url - URL字符串
     * @returns {object} 参数对象
     */
    function parseUrlParams(url = window.location.href) {
        const params = {};
        const urlObj = new URL(url);
        for (const [key, value] of urlObj.searchParams) {
            params[key] = value;
        }
        return params;
    }

    /**
     * 构建URL参数
     * @param {object} params - 参数对象
     * @returns {string} 参数字符串
     */
    function buildUrlParams(params) {
        const searchParams = new URLSearchParams();
        for (const [key, value] of Object.entries(params)) {
            if (value !== null && value !== undefined) {
                searchParams.append(key, value);
            }
        }
        return searchParams.toString();
    }

    /**
     * 安全的JSON解析
     * @param {string} jsonStr - JSON字符串
     * @param {any} defaultValue - 默认值
     * @returns {any} 解析结果
     */
    function safeJsonParse(jsonStr, defaultValue = null) {
        try {
            return JSON.parse(jsonStr);
        } catch (e) {
            return defaultValue;
        }
    }

    /**
     * 安全的JSON字符串化
     * @param {any} obj - 要字符串化的对象
     * @param {string} defaultValue - 默认值
     * @returns {string} JSON字符串
     */
    function safeJsonStringify(obj, defaultValue = '{}') {
        try {
            return JSON.stringify(obj);
        } catch (e) {
            return defaultValue;
        }
    }

    /**
     * 异步延迟
     * @param {number} ms - 延迟毫秒数
     * @returns {Promise} Promise对象
     */
    function sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * 重试函数
     * @param {function} fn - 要重试的函数
     * @param {number} maxRetries - 最大重试次数
     * @param {number} delay - 重试延迟
     * @returns {Promise} Promise对象
     */
    async function retry(fn, maxRetries = 3, delay = 1000) {
        for (let i = 0; i < maxRetries; i++) {
            try {
                return await fn();
            } catch (error) {
                if (i === maxRetries - 1) throw error;
                await sleep(delay);
            }
        }
    }

    /**
     * 对象差异比较
     * @param {object} obj1 - 对象1
     * @param {object} obj2 - 对象2
     * @returns {object} 差异对象
     */
    function objectDiff(obj1, obj2) {
        const diff = {};
        const allKeys = new Set([...Object.keys(obj1), ...Object.keys(obj2)]);
        
        for (const key of allKeys) {
            if (obj1[key] !== obj2[key]) {
                diff[key] = { old: obj1[key], new: obj2[key] };
            }
        }
        
        return diff;
    }

    /**
     * 数组去重
     * @param {Array} arr - 数组
     * @returns {Array} 去重后的数组
     */
    function uniqueArray(arr) {
        return [...new Set(arr)];
    }

    /**
     * 截断字符串
     * @param {string} str - 字符串
     * @param {number} length - 最大长度
     * @param {string} suffix - 后缀
     * @returns {string} 截断后的字符串
     */
    function truncateString(str, length, suffix = '...') {
        if (str.length <= length) return str;
        return str.substring(0, length) + suffix;
    }

    /**
     * 驼峰转下划线
     * @param {string} str - 驼峰字符串
     * @returns {string} 下划线字符串
     */
    function camelToSnake(str) {
        return str.replace(/([A-Z])/g, '_$1').toLowerCase();
    }

    /**
     * 下划线转驼峰
     * @param {string} str - 下划线字符串
     * @returns {string} 驼峰字符串
     */
    function snakeToCamel(str) {
        return str.replace(/_([a-z])/g, (match, letter) => letter.toUpperCase());
    }

    /**
     * 检查值是否为空
     * @param {any} value - 值
     * @returns {boolean} 是否为空
     */
    function isEmpty(value) {
        if (value == null) return true;
        if (typeof value === 'string') return value.trim() === '';
        if (Array.isArray(value)) return value.length === 0;
        if (typeof value === 'object') return Object.keys(value).length === 0;
        return false;
    }

    /**
     * 获取嵌套对象值
     * @param {object} obj - 对象
     * @param {string} path - 路径
     * @param {any} defaultValue - 默认值
     * @returns {any} 值
     */
    function getNestedValue(obj, path, defaultValue = undefined) {
        const keys = path.split('.');
        let result = obj;
        
        for (const key of keys) {
            if (result == null || typeof result !== 'object') {
                return defaultValue;
            }
            result = result[key];
        }
        
        return result !== undefined ? result : defaultValue;
    }

    /**
     * 设置嵌套对象值
     * @param {object} obj - 对象
     * @param {string} path - 路径
     * @param {any} value - 值
     */
    function setNestedValue(obj, path, value) {
        const keys = path.split('.');
        const lastKey = keys.pop();
        let current = obj;
        
        for (const key of keys) {
            if (!(key in current) || typeof current[key] !== 'object') {
                current[key] = {};
            }
            current = current[key];
        }
        
        current[lastKey] = value;
    }

    /**
     * 性能监控类
     */
    class PerformanceMonitor {
        constructor() {
            this.marks = new Map();
            this.measures = new Map();
        }

        mark(name) {
            const timestamp = performance.now();
            this.marks.set(name, timestamp);
            if (performance.mark) {
                performance.mark(name);
            }
        }

        measure(name, startMark, endMark) {
            const startTime = this.marks.get(startMark);
            const endTime = this.marks.get(endMark);
            
            if (startTime && endTime) {
                const duration = endTime - startTime;
                this.measures.set(name, duration);
                
                if (performance.measure) {
                    performance.measure(name, startMark, endMark);
                }
                
                return duration;
            }
            
            return null;
        }

        getEntries() {
            return {
                marks: Object.fromEntries(this.marks),
                measures: Object.fromEntries(this.measures)
            };
        }

        clear() {
            this.marks.clear();
            this.measures.clear();
            if (performance.clearMarks) {
                performance.clearMarks();
            }
            if (performance.clearMeasures) {
                performance.clearMeasures();
            }
        }
    }

    // 创建全局性能监控实例
    const performanceMonitor = new PerformanceMonitor();

    // ========================================
    // 第二部分：日志管理器模块 (logger.js)
    // ========================================

    class Logger {
        constructor() {
            this.logs = [];
            this.maxLogs = 1000; // 最大日志条数
            this.debugMode = false;
            this.listeners = [];
            this.originalConsole = {}; // 存储原始console方法
            
            // 日志级别
            this.levels = {
                debug: 0,
                info: 1,
                success: 2,
                warning: 3,
                error: 4
            };
            
            // 基础监控配置
            this.monitoring = {
                enabled: true,
                realTimeConsole: true // 实时控制台输出
            };
            
            this.loadFromStorage();
            this.interceptConsole();
            this.setupBasicMonitoring();
        }
        
        /**
         * 从本地存储加载日志
         */
        loadFromStorage() {
            try {
                const saved = localStorage.getItem('ota-system-logs');
                if (saved) {
                    this.logs = JSON.parse(saved);
                    // 限制加载的日志数量
                    if (this.logs.length > this.maxLogs) {
                        this.logs = this.logs.slice(-this.maxLogs);
                    }
                }
            } catch (error) {
                console.warn('加载日志失败:', error);
                this.logs = [];
            }
        }
        
        /**
         * 保存日志到本地存储
         */
        saveToStorage() {
            try {
                // 只保存最近的日志
                const logsToSave = this.logs.slice(-this.maxLogs);
                localStorage.setItem('ota-system-logs', JSON.stringify(logsToSave));
            } catch (error) {
                console.warn('保存日志失败:', error);
            }
        }
        
        /**
         * 拦截console方法
         */
        interceptConsole() {
            if (!this.monitoring.enabled) return;
            
            // 保存原始方法
            this.originalConsole = {
                log: console.log,
                warn: console.warn,
                error: console.error,
                info: console.info
            };
            
            // 拦截console.error
            console.error = (...args) => {
                this.originalConsole.error.apply(console, args);
                this.log(`Console Error: ${args.join(' ')}`, 'error', {
                    type: 'console_error',
                    args: args
                });
            };
            
            // 拦截console.warn
            console.warn = (...args) => {
                this.originalConsole.warn.apply(console, args);
                this.log(`Console Warning: ${args.join(' ')}`, 'warning', {
                    type: 'console_warning',
                    args: args
                });
            };
        }
        
        /**
         * 设置基础监控
         */
        setupBasicMonitoring() {
            if (!this.monitoring.enabled) return;
            
            // 监听未捕获的错误
            window.addEventListener('error', (event) => {
                this.log('未捕获的错误', 'error', {
                    type: 'uncaught_error',
                    message: event.message,
                    filename: event.filename,
                    lineno: event.lineno,
                    colno: event.colno,
                    error: event.error ? event.error.stack : null
                });
            });
            
            // 监听未处理的Promise拒绝
            window.addEventListener('unhandledrejection', (event) => {
                this.log('未处理的Promise拒绝', 'error', {
                    type: 'unhandled_promise_rejection',
                    reason: event.reason,
                    promise: event.promise
                });
            });
        }
        
        /**
         * 记录日志
         * @param {string} message - 日志消息
         * @param {string} level - 日志级别
         * @param {object} data - 附加数据
         */
        log(message, level = 'info', data = {}) {
            const timestamp = new Date().toISOString();
            const logEntry = {
                id: generateId(12),
                timestamp,
                message,
                level,
                data,
                userAgent: navigator.userAgent,
                url: window.location.href
            };
            
            // 添加到日志数组
            this.logs.push(logEntry);
            
            // 限制日志数量
            if (this.logs.length > this.maxLogs) {
                this.logs = this.logs.slice(-this.maxLogs);
            }
            
            // 实时控制台输出
            if (this.monitoring.realTimeConsole) {
                this.outputToConsole(logEntry);
            }
            
            // 通知监听器
            this.notifyListeners(logEntry);
            
            // 异步保存到存储
            setTimeout(() => this.saveToStorage(), 0);
        }
        
        /**
         * 输出到控制台
         * @param {object} logEntry - 日志条目
         */
        outputToConsole(logEntry) {
            const { message, level, data } = logEntry;
            const timestamp = new Date(logEntry.timestamp).toLocaleTimeString();
            const prefix = `[${timestamp}] [${level.toUpperCase()}]`;
            
            switch (level) {
                case 'error':
                    this.originalConsole.error(prefix, message, data);
                    break;
                case 'warning':
                    this.originalConsole.warn(prefix, message, data);
                    break;
                case 'success':
                    this.originalConsole.log(`%c${prefix} ${message}`, 'color: green', data);
                    break;
                case 'debug':
                    if (this.debugMode) {
                        this.originalConsole.log(`%c${prefix} ${message}`, 'color: gray', data);
                    }
                    break;
                default:
                    this.originalConsole.info(prefix, message, data);
            }
        }
        
        /**
         * 通知监听器
         * @param {object} logEntry - 日志条目
         */
        notifyListeners(logEntry) {
            this.listeners.forEach(listener => {
                try {
                    listener(logEntry);
                } catch (error) {
                    this.originalConsole.error('日志监听器错误:', error);
                }
            });
        }
        
        /**
         * 添加日志监听器
         * @param {function} listener - 监听器函数
         */
        addListener(listener) {
            if (typeof listener === 'function') {
                this.listeners.push(listener);
            }
        }
        
        /**
         * 移除日志监听器
         * @param {function} listener - 监听器函数
         */
        removeListener(listener) {
            const index = this.listeners.indexOf(listener);
            if (index > -1) {
                this.listeners.splice(index, 1);
            }
        }
        
        /**
         * 获取日志
         * @param {object} options - 选项
         * @returns {Array} 日志数组
         */
        getLogs(options = {}) {
            let logs = [...this.logs];
            
            // 按级别过滤
            if (options.level) {
                logs = logs.filter(log => log.level === options.level);
            }
            
            // 按时间范围过滤
            if (options.startTime) {
                const startTime = new Date(options.startTime);
                logs = logs.filter(log => new Date(log.timestamp) >= startTime);
            }
            
            if (options.endTime) {
                const endTime = new Date(options.endTime);
                logs = logs.filter(log => new Date(log.timestamp) <= endTime);
            }
            
            // 限制数量
            if (options.limit) {
                logs = logs.slice(-options.limit);
            }
            
            return logs;
        }
        
        /**
         * 清除日志
         */
        clearLogs() {
            this.logs = [];
            this.saveToStorage();
            this.log('日志已清除', 'info', { type: 'logs_cleared' });
        }
        
        /**
         * 导出日志
         * @param {string} format - 导出格式 ('json' | 'csv' | 'txt')
         * @returns {string} 导出的日志内容
         */
        exportLogs(format = 'json') {
            switch (format) {
                case 'json':
                    return JSON.stringify(this.logs, null, 2);
                case 'csv':
                    return this.logsToCSV();
                case 'txt':
                    return this.logsToText();
                default:
                    return JSON.stringify(this.logs, null, 2);
            }
        }
        
        /**
         * 转换日志为CSV格式
         * @returns {string} CSV格式的日志
         */
        logsToCSV() {
            if (this.logs.length === 0) return '';
            
            const headers = ['timestamp', 'level', 'message', 'data'];
            const csvRows = [headers.join(',')];
            
            this.logs.forEach(log => {
                const row = [
                    log.timestamp,
                    log.level,
                    `"${log.message.replace(/"/g, '""')}"`,
                    `"${JSON.stringify(log.data).replace(/"/g, '""')}"`
                ];
                csvRows.push(row.join(','));
            });
            
            return csvRows.join('\n');
        }
        
        /**
         * 转换日志为文本格式
         * @returns {string} 文本格式的日志
         */
        logsToText() {
            return this.logs.map(log => {
                const timestamp = new Date(log.timestamp).toLocaleString();
                const dataStr = Object.keys(log.data).length > 0 ? 
                    ` | Data: ${JSON.stringify(log.data)}` : '';
                return `[${timestamp}] [${log.level.toUpperCase()}] ${log.message}${dataStr}`;
            }).join('\n');
        }
        
        /**
         * 设置调试模式
         * @param {boolean} enabled - 是否启用
         */
        setDebugMode(enabled) {
            this.debugMode = enabled;
            this.log(`调试模式${enabled ? '已启用' : '已禁用'}`, 'info', {
                type: 'debug_mode_changed',
                enabled
            });
        }
        
        /**
         * 获取日志统计
         * @returns {object} 统计信息
         */
        getStats() {
            const stats = {
                total: this.logs.length,
                byLevel: {}
            };
            
            // 按级别统计
            Object.keys(this.levels).forEach(level => {
                stats.byLevel[level] = this.logs.filter(log => log.level === level).length;
            });
            
            // 最近24小时的日志
            const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
            stats.last24Hours = this.logs.filter(log => 
                new Date(log.timestamp) >= oneDayAgo
            ).length;
            
            return stats;
        }
    }

    // 创建单例实例
    let loggerInstance = null;

    /**
     * 获取日志服务实例
     * @returns {Logger} 日志服务实例
     */
    function getLogger() {
        if (!loggerInstance) {
            loggerInstance = new Logger();
            // 记录系统启动
            loggerInstance.log('OTA订单处理系统启动', 'info', {
                type: 'system_start',
                userAgent: navigator.userAgent,
                timestamp: new Date().toISOString()
            });
        }
        return loggerInstance;
    }

    // 创建默认实例以保持向后兼容性
    const logger = getLogger();

    // ========================================
    // 第三部分：监控包装器模块 (monitoring-wrapper.js)
    // ========================================

    /**
     * 监控包装器类
     * 用于包装函数并添加监控功能
     */
    class MonitoringWrapper {
        constructor() {
            this.wrappedFunctions = new Set();
            this.performanceMarks = new Map();
        }

        /**
         * 包装工厂函数添加监控
         * @param {string} functionName - 函数名称
         * @param {function} originalFunction - 原始函数
         * @returns {function} 包装后的函数
         */
        wrapFactoryFunction(functionName, originalFunction) {
            if (this.wrappedFunctions.has(functionName)) {
                return originalFunction;
            }

            this.wrappedFunctions.add(functionName);

            return (...args) => {
                const startTime = performance.now();
                const markStart = `${functionName}_start`;
                const markEnd = `${functionName}_end`;
                const measureName = `${functionName}_duration`;

                try {
                    // 性能标记开始
                    performance.mark(markStart);

                    // 调用原函数
                    const result = originalFunction.apply(this, args);

                    // 性能标记结束
                    performance.mark(markEnd);
                    
                    // 测量执行时间
                    const endTime = performance.now();
                    const duration = endTime - startTime;
                    
                    // 存储性能数据
                    this.performanceMarks.set(functionName, {
                        duration,
                        timestamp: new Date().toISOString(),
                        args: args.length
                    });

                    // 记录到日志（如果可用）
                    if (window.getLogger) {
                        const logger = window.getLogger();
                        logger.log(`工厂函数执行: ${functionName}`, 'debug', {
                            type: 'factory_function_execution',
                            functionName,
                            duration: `${duration.toFixed(2)}ms`,
                            argsCount: args.length
                        });
                    }

                    return result;
                } catch (error) {
                    // 记录错误
                    if (window.getLogger) {
                        const logger = window.getLogger();
                        logger.log(`工厂函数执行失败: ${functionName}`, 'error', {
                            type: 'factory_function_error',
                            functionName,
                            error: error.message,
                            stack: error.stack
                        });
                    }
                    throw error;
                }
            };
        }

        /**
         * 包装所有工厂函数
         */
        wrapAllFactoryFunctions() {
            const factoryFunctions = [
                'getLogger',
                'getApiService',
                'getFormManager',
                'getEventManager',
                'getStateManager',
                'getUIManager',
                'getGeminiService',
                'getMultiOrderManager',
                'getOrderHistoryManager',
                'getImageUploadManager',
                'getCurrencyConverter',
                'getFlightInfoService'
            ];

            factoryFunctions.forEach(funcName => {
                if (window[funcName] && typeof window[funcName] === 'function') {
                    window[funcName] = this.wrapFactoryFunction(funcName, window[funcName]);
                }
            });

            // 包装OTA命名空间下的工厂函数
            if (window.OTA) {
                Object.keys(window.OTA).forEach(key => {
                    if (key.startsWith('get') && typeof window.OTA[key] === 'function') {
                        window.OTA[key] = this.wrapFactoryFunction(`OTA.${key}`, window.OTA[key]);
                    }
                });
            }
        }

        /**
         * 包装Gemini服务方法
         */
        wrapGeminiService() {
            if (!window.OTA || !window.OTA.geminiService) return;

            const geminiService = window.OTA.geminiService;
            const geminiMethods = ['analyzeOrder', 'processMultiOrder', 'generatePrompt'];

            geminiMethods.forEach(methodName => {
                if (geminiService[methodName] && typeof geminiService[methodName] === 'function') {
                    const originalMethod = geminiService[methodName];
                    geminiService[methodName] = this.wrapFactoryFunction(
                        `geminiService.${methodName}`, 
                        originalMethod.bind(geminiService)
                    );
                }
            });

            // 记录包装完成
            const logger = window.getLogger && window.getLogger();
            if (logger) {
                logger.log('Gemini服务监控已启用', 'info', {
                    type: 'gemini_monitoring_enabled',
                    methods: geminiMethods
                });
            }
        }

        /**
         * 获取包装统计
         * @returns {object} 统计信息
         */
        getWrapperStats() {
            return {
                wrappedFunctionsCount: this.wrappedFunctions.size,
                wrappedFunctions: Array.from(this.wrappedFunctions),
                performanceData: Object.fromEntries(this.performanceMarks)
            };
        }

        /**
         * 获取性能数据
         * @returns {object} 性能数据
         */
        getPerformanceData() {
            return Object.fromEntries(this.performanceMarks);
        }

        /**
         * 清除性能数据
         */
        clearPerformanceData() {
            this.performanceMarks.clear();
        }
    }

    // 创建全局监控包装器实例
    const monitoringWrapper = new MonitoringWrapper();

    // ========================================
    // 统一API暴露
    // ========================================

    // 暴露到OTA命名空间
    window.OTA.utils = {
        debounce,
        throttle,
        deepClone,
        formatDate,
        formatTime,
        formatFileSize,
        generateId,
        isValidEmail,
        isValidPhone,
        normalizePhoneNumber,
        parseDate,
        convertToApiDateFormat,
        daysDifference,
        isMobile,
        getBrowserInfo,
        copyToClipboard,
        downloadFile,
        parseUrlParams,
        buildUrlParams,
        safeJsonParse,
        safeJsonStringify,
        sleep,
        retry,
        objectDiff,
        uniqueArray,
        truncateString,
        camelToSnake,
        snakeToCamel,
        isEmpty,
        getNestedValue,
        setNestedValue,
        performanceMonitor
    };

    window.OTA.logger = logger;
    window.OTA.getLogger = getLogger;
    window.OTA.monitoringWrapper = monitoringWrapper;

    // 向后兼容：暴露到全局window对象
    window.utils = window.OTA.utils;
    window.logger = logger;
    window.getLogger = getLogger;
    window.monitoringWrapper = monitoringWrapper;

    // 注册到OTA注册中心
    if (window.OTA && window.OTA.Registry) {
        window.OTA.Registry.registerService('logger', logger, '@OTA_LOGGER_SERVICE');
        window.OTA.Registry.registerFactory('getLogger', getLogger, '@OTA_LOGGER_FACTORY');
        window.OTA.Registry.registerService('utils', window.OTA.utils, '@OTA_UTILS_SERVICE');
        window.OTA.Registry.registerService('monitoringWrapper', monitoringWrapper, '@OTA_MONITORING_WRAPPER');
    }

    // 等待DOM加载完成后自动包装工厂函数
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(() => {
                monitoringWrapper.wrapAllFactoryFunctions();
            }, 100); // 延迟确保所有工厂函数都已定义
        });
    } else {
        // 如果DOM已经加载完成，立即执行
        setTimeout(() => {
            monitoringWrapper.wrapAllFactoryFunctions();
        }, 100);
    }

})();
