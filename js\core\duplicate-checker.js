/**
 * @OTA_CORE 简化的重复开发检查器
 * 🏷️ 标签: @OTA_DUPLICATE_CHECKER
 * 📝 功能: 基础的重复开发检测，已简化复杂逻辑
 * 🧹 已清理: 移除了过度复杂的检测机制
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};

(function() {
    'use strict';

    /**
     * @OTA_CORE 简化的重复开发检查器
     * 提供基础的重复检测功能，避免过度复杂化
     */
    const DuplicateChecker = {
        // 核心函数白名单（简化版）
        coreServices: new Set([
            'getLogger', 'getAppState', 'getAPIService', 'getGeminiService',
            'getMultiOrderManager', 'getImageUploadManager', 'getCurrencyConverter'
        ]),

        // 简化的检测结果
        detectedIssues: [],

        /**
         * 简化的重复检测 - 只检查核心服务
         */
        checkForDuplicates() {
            const issues = [];

            // 只检查核心服务函数
            this.coreServices.forEach(serviceName => {
                if (typeof window[serviceName] === 'function') {
                    // 检查是否在OTA命名空间中也存在
                    if (window.OTA && window.OTA[serviceName.replace('get', '').toLowerCase()]) {
                        issues.push({
                            service: serviceName,
                            message: `✅ ${serviceName} 正常注册`,
                            status: 'ok'
                        });
                    }
                } else {
                    issues.push({
                        service: serviceName,
                        message: `❌ ${serviceName} 未找到`,
                        status: 'missing'
                    });
                }
            });

            this.detectedIssues = issues;

            return {
                summary: {
                    totalChecked: this.coreServices.size,
                    foundServices: issues.filter(i => i.status === 'ok').length,
                    missingServices: issues.filter(i => i.status === 'missing').length
                },
                issues,
                timestamp: new Date().toISOString()
            };
        },

        /**
         * 简化的报告生成
         */
        generateReport() {
            const result = this.checkForDuplicates();
            return {
                timestamp: new Date().toISOString(),
                summary: result.summary,
                issues: result.issues,
                status: result.summary.missingServices === 0 ? 'healthy' : 'needs_attention'
            };
        },
    };

    // 注册到OTA命名空间
    window.OTA.DuplicateChecker = DuplicateChecker;

    // 简化的全局调试命令
    window.checkDuplicates = () => {
        const report = DuplicateChecker.generateReport();
        console.group('🔍 简化的重复检查报告');
        console.table(report.summary);
        if (report.issues.length > 0) {
            console.group('📋 检查结果');
            report.issues.forEach(issue => console.log(issue.message));
            console.groupEnd();
        }
        console.groupEnd();
        return report;
    };

    // 初始化日志（受特性开关控制）
    try {
        const enabled = window.OTA?.featureToggle?.isEnabled('enableDuplicateChecker');
        const logger = window.getLogger ? window.getLogger() : console;
        if (logger && logger.log) {
            if (enabled) {
                logger.log('🚀 简化的重复开发检查器已初始化', 'info', {
                    coreServices: DuplicateChecker.coreServices.size
                });
            } else {
                console.log('🔍 重复检查器已加载（未启用，受特性开关控制）');
            }
        }
    } catch (_) { /* 忽略 */ }

})();