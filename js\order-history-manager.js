/**
 * 依赖标签（Dependency Tags）
 * 文件: js/order-history-manager.js
 * 角色: 订单历史记录与最近使用数据（本地存储、TTL、PII保护）
 * 上游依赖(直接使用): <PERSON>gger(getLogger), AppState/localStorage
 * 下游被依赖: Processor / UIManager / MultiOrderManagerV2
 * 事件: 无；建议增加隐私模式和掩码
 * 更新时间: 2025-08-09
 */
/**
 * 历史订单管理器
 * 负责订单历史记录的存储、检索和管理
 * <AUTHOR>
 * @version 1.0.0
 */

// 获取依赖模块 - 使用统一的服务定位器

class OrderHistoryManager {
    constructor() {
        this.storageKey = 'ota_order_history';
        this.maxHistorySize = 1000; // 最大存储1000条历史记录
        this.currentUserEmail = null; // 当前用户邮箱
        this.init();
    }

    /**
     * 初始化历史订单管理器
     */
    init() {
        // 迁移旧数据格式到新的按账号存储格式
        this.migrateOldData();

        // 确保localStorage中有历史记录对象
        const historyData = localStorage.getItem(this.storageKey);
        if (!historyData) {
            localStorage.setItem(this.storageKey, JSON.stringify({}));
        }

        // 初始化事件监听器
        this.initEventListeners();

        const logger = getLogger();
        if (logger) {
            logger.log('历史订单管理器已初始化（按账号存储）', 'info');
        }
    }

    /**
     * 迁移旧数据格式到新格式
     * 将数组格式转换为按账号存储的对象格式
     */
    migrateOldData() {
        try {
            const existingData = localStorage.getItem(this.storageKey);
            if (existingData) {
                const parsed = JSON.parse(existingData);

                // 如果是旧的数组格式，进行迁移
                if (Array.isArray(parsed) && parsed.length > 0) {
                    const migratedData = {
                        '<EMAIL>': parsed // 将旧数据存储到特殊账号下
                    };
                    localStorage.setItem(this.storageKey, JSON.stringify(migratedData));

                    const logger = getLogger();
                    if (logger) {
                        logger.log(`已迁移 ${parsed.length} 条历史订单数据到新格式`, 'info');
                    }
                }
            }
        } catch (error) {
            const logger = getLogger();
            if (logger) {
                logger.log(`数据迁移失败: ${error.message}`, 'error');
            }
        }
    }

    /**
     * 设置当前用户邮箱
     * @param {string} email - 用户邮箱
     */
    setCurrentUser(email) {
        this.currentUserEmail = email;
        const logger = getLogger();
        if (logger) {
            logger.log(`历史订单管理器已切换到用户: ${email}`, 'info');
        }
    }

    /**
     * 获取当前用户邮箱
     * @returns {string} 当前用户邮箱
     */
    getCurrentUser() {
        if (!this.currentUserEmail) {
            // 尝试多种方式获取当前用户
            try {
                // 方式1: 从应用状态获取
                const appState = getAppState && getAppState();
                if (appState) {
                    const userEmail = appState.get('auth.user.email');
                    if (userEmail && userEmail !== 'undefined' && userEmail !== 'null') {
                        this.currentUserEmail = userEmail;
                        return this.currentUserEmail;
                    }
                }
                
                // 方式2: 从localStorage获取
                const savedUser = localStorage.getItem('ota-current-user');
                if (savedUser && savedUser !== 'undefined' && savedUser !== 'null') {
                    this.currentUserEmail = savedUser;
                    return this.currentUserEmail;
                }
                
                // 方式3: 从OTA状态获取
                if (window.OTA && window.OTA.appState) {
                    const otaUser = window.OTA.appState.get && window.OTA.appState.get('auth.user.email');
                    if (otaUser && otaUser !== 'undefined' && otaUser !== 'null') {
                        this.currentUserEmail = otaUser;
                        return this.currentUserEmail;
                    }
                }
                
                // 默认用户
                this.currentUserEmail = '<EMAIL>';
                
            } catch (error) {
                const logger = getLogger();
                if (logger) {
                    logger.log(`获取当前用户失败，使用默认用户: ${error.message}`, 'warning');
                }
                this.currentUserEmail = '<EMAIL>';
            }
        }
        return this.currentUserEmail;
    }

    /**
     * 添加新的订单记录
     * @param {Object} orderData - 订单数据
     * @param {string} orderId - GoMyHire API返回的订单ID
     * @param {Object} apiResponse - 完整的API响应
     */
    async addOrder(orderData, orderId, apiResponse = null) {
        try {
            const logger = getLogger();
            logger?.log(`开始添加订单历史记录: ${orderId}`, 'info');
            
            // === 使用现有OTA系统处理订单 ===
            let processedPrice = orderData.otaPrice || orderData.ota_price || '';
            let otaProcessing = null;
            
            // 严格检查是否为Fliggy订单并处理价格
            const otaRef = orderData.otaReferenceNumber || orderData.ota_reference_number || '';
            const otaChannel = orderData.otaChannel || orderData.ota_channel || '';
            
            // Fliggy特征识别（主路径：策略检测；回退：原有逻辑）
            let isFliggyOrder = false;
            try {
                if (typeof FliggyOTAStrategy !== 'undefined') {
                    const contentRes = FliggyOTAStrategy.detectFromContent(`${otaRef} ${otaChannel}`);
                    const refRes = FliggyOTAStrategy.detectByReference(otaRef);
                    isFliggyOrder = (contentRes.detectedChannel === 'Fliggy' && contentRes.confidence >= 0.85)
                                  || (refRes.detectedChannel === 'Fliggy' && refRes.confidence >= 0.85);
                }
            } catch (_) { /* 忽略，走回退 */ }

            if (!isFliggyOrder) {
                // 回退：订单编号+19位数字 或 渠道标识
                isFliggyOrder = /订单编号\d{19}/.test(otaRef) ||
                                otaChannel.toLowerCase().includes('fliggy') ||
                                otaRef.toLowerCase().includes('fliggy');
            }
                                 
            if (isFliggyOrder) {
                logger?.log('识别为Fliggy订单，启用专属处理', 'info', {
                    参考号: otaRef,
                    渠道: otaChannel,
                    匹配方式: /订单编号\d{19}/.test(otaRef) ? '订单编号模式' : '渠道标识'
                });
                
                // 使用现有的OTA定制化引擎（异步）
                const customEngine = window.OTA?.customizationEngine;
                if (customEngine && processedPrice) {
                    try {
                        const processResult = await customEngine.processOrder('Fliggy', orderData, parseFloat(processedPrice));
                        if (processResult.success && processResult.priceResult) {
                            processedPrice = processResult.priceResult.finalPrice;
                            otaProcessing = {
                                recognized: true,
                                channel: 'Fliggy',
                                recognitionMethod: /订单编号\d{19}/.test(otaRef) ? '订单编号模式' : '渠道标识',
                                originalPrice: orderData.otaPrice || orderData.ota_price,
                                processedPrice: processedPrice,
                                priceAdjustments: processResult.priceResult.adjustments || [],
                                // Fliggy专属：客户名字处理信息
                                customerNameSource: processResult.processedData?.customer_name ? 'processed' : 'original',
                                // Fliggy专属：地址翻译信息
                                addressTranslation: {
                                    pickup: processResult.processedData?.pickup_location_en || null,
                                    dropoff: processResult.processedData?.dropoff_location_en || null,
                                    source: processResult.processedData?.pickup_translation_source || 'none'
                                }
                            };
                            logger?.log(`Fliggy订单专属处理完成`, 'info', {
                                价格: `${orderData.otaPrice || orderData.ota_price} -> ${processedPrice}`,
                                客户名: processResult.processedData?.customer_name || '未处理',
                                地址翻译: otaProcessing.addressTranslation.source !== 'none' ? '已翻译' : '无需翻译'
                            });
                        }
                    } catch (error) {
                        logger?.log(`Fliggy订单专属处理失败: ${error.message}`, 'error');
                    }
                }
            } else {
                // 非Fliggy订单使用通用处理（如果需要的话）
                logger?.log('非Fliggy订单，使用通用处理', 'info', {
                    参考号: otaRef,
                    渠道: otaChannel
                });
            }
            
            const historyRecord = {
                // 基础信息
                id: this.generateHistoryId(),
                orderId: orderId,
                timestamp: new Date().toISOString(),
                
                // OTA处理结果（如果有）
                ...(otaProcessing && { otaProcessing }),
                
                // 订单内容
                orderData: {
                    // 基本信息
                    subCategoryId: orderData.subCategoryId || orderData.sub_category_id || '',
                    otaReferenceNumber: orderData.otaReferenceNumber || orderData.ota_reference_number || '',
                    otaChannel: orderData.otaChannel || orderData.ota_channel || '',
                    carTypeId: orderData.carTypeId || orderData.car_type_id || '',
                    
                    // 客户信息
                    customerName: orderData.customerName || orderData.customer_name || '',
                    customerContact: orderData.customerContact || orderData.customer_contact || '',
                    customerEmail: orderData.customerEmail || orderData.customer_email || '',
                    flightInfo: orderData.flightInfo || orderData.flight_info || '',
                    
                    // 行程信息
                    pickup: orderData.pickup || orderData.pickup_location || '',
                    destination: orderData.destination || orderData.dropoff_location || '',
                    date: orderData.date || orderData.pickup_date || '',
                    time: orderData.time || orderData.pickup_time || '',
                    passengerNumber: orderData.passengerNumber || orderData.passenger_number || '',
                    luggageNumber: orderData.luggageNumber || orderData.luggage_count || '',
                    
                    // 其他信息
                    specialRequests: orderData.specialRequests || orderData.extra_requirement || '',
                    otaPrice: orderData.otaPrice || orderData.ota_price || '',
                    processedPrice: processedPrice, // 保存处理后的价格
                    drivingRegionId: orderData.drivingRegionId || '',
                    languagesIdArray: orderData.languagesIdArray || {}
                },
                
                // 元数据
                metadata: {
                    userEmail: getAppState() ? getAppState().get('auth.user.email') : '',
                    createdBy: getAppState() ? getAppState().get('auth.user.name') : '',
                    apiResponse: apiResponse ? {
                        success: apiResponse.success,
                        message: apiResponse.message,
                        data: apiResponse.data
                    } : null
                }
            };

            // 获取当前用户和所有历史记录
            const currentUser = this.getCurrentUser();
            const allHistory = this.getAllUsersHistory();
            const userHistory = allHistory[currentUser] || [];

            logger?.log(`当前用户: ${currentUser}, 现有订单数: ${userHistory.length}`, 'info');

            // 添加新记录到开头
            userHistory.unshift(historyRecord);

            // 限制历史记录数量
            if (userHistory.length > this.maxHistorySize) {
                const removedCount = userHistory.length - this.maxHistorySize;
                userHistory.splice(this.maxHistorySize);
                logger?.log(`删除了 ${removedCount} 条旧记录以保持大小限制`, 'info');
            }

            // 更新用户的历史记录
            allHistory[currentUser] = userHistory;

            // 保存到localStorage
            const historyJson = JSON.stringify(allHistory);
            localStorage.setItem(this.storageKey, historyJson);
            
            logger?.log(`订单历史记录已添加: ${orderId}, 总计: ${userHistory.length} 条`, 'success');
            logger?.log('历史记录数据大小:', Math.round(historyJson.length / 1024) + 'KB', 'info');
            
            return historyRecord;
            
        } catch (error) {
            const logger = getLogger();
            if (logger) {
                logger.log(`添加订单历史记录失败: ${error.message}`, 'error');
                logger.log('错误详情:', { error: error.stack, orderData, orderId, apiResponse }, 'error');
            }
            throw error;
        }
    }

    /**
     * 获取当前用户的历史订单
     * @returns {Array} 历史订单数组
     */
    getHistory() {
        try {
            const currentUser = this.getCurrentUser();
            const historyJson = localStorage.getItem(this.storageKey);
            
            if (!historyJson) {
                const logger = getLogger();
                logger?.log('localStorage中未找到历史订单数据', 'info');
                return [];
            }
            
            const allHistory = JSON.parse(historyJson);
            
            if (!allHistory || typeof allHistory !== 'object') {
                const logger = getLogger();
                logger?.log('历史订单数据格式不正确', 'warning');
                return [];
            }

            // 返回当前用户的历史记录，如果不存在则返回空数组
            const userHistory = allHistory[currentUser] || [];
            
            const logger = getLogger();
            logger?.log(`为用户 ${currentUser} 获取到 ${userHistory.length} 条历史订单`, 'info');
            
            // 确保返回的是数组
            return Array.isArray(userHistory) ? userHistory : [];
        } catch (error) {
            const logger = getLogger();
            if (logger) {
                logger.log(`获取历史订单失败: ${error.message}`, 'error');
            }
            
            // 尝试清理损坏的数据
            try {
                localStorage.removeItem(this.storageKey);
                localStorage.setItem(this.storageKey, JSON.stringify({}));
                logger?.log('已清理损坏的历史订单数据', 'warning');
            } catch (cleanupError) {
                logger?.log(`清理数据失败: ${cleanupError.message}`, 'error');
            }
            
            return [];
        }
    }

    /**
     * 获取所有用户的历史订单（管理员功能）
     * @returns {Object} 按用户分组的历史订单对象
     */
    getAllUsersHistory() {
        try {
            const historyJson = localStorage.getItem(this.storageKey);
            return historyJson ? JSON.parse(historyJson) : {};
        } catch (error) {
            const logger = getLogger();
            if (logger) {
                logger.log(`获取所有用户历史订单失败: ${error.message}`, 'error');
            }
            return {};
        }
    }

    /**
     * 根据条件搜索历史订单
     * @param {Object} criteria - 搜索条件
     * @returns {Array} 匹配的订单数组
     */
    searchOrders(criteria = {}) {
        const history = this.getHistory();
        
        return history.filter(record => {
            // 按订单ID搜索 - 安全检查orderId类型
            if (criteria.orderId) {
                const recordOrderId = String(record.orderId || '').toLowerCase();
                const searchOrderId = String(criteria.orderId || '').toLowerCase();
                if (!recordOrderId.includes(searchOrderId)) {
                    return false;
                }
            }
            
            // 按OTA参考号搜索
            if (criteria.otaReference && !record.orderData.otaReferenceNumber.toLowerCase().includes(criteria.otaReference.toLowerCase())) {
                return false;
            }
            
            // 按客户姓名搜索
            if (criteria.customerName && !record.orderData.customerName.toLowerCase().includes(criteria.customerName.toLowerCase())) {
                return false;
            }
            
            // 按客户邮箱搜索
            if (criteria.customerEmail && !record.orderData.customerEmail.toLowerCase().includes(criteria.customerEmail.toLowerCase())) {
                return false;
            }
            
            // 按日期范围搜索
            if (criteria.dateFrom) {
                const recordDate = new Date(record.timestamp);
                const fromDate = new Date(criteria.dateFrom);
                if (recordDate < fromDate) return false;
            }
            
            if (criteria.dateTo) {
                const recordDate = new Date(record.timestamp);
                const toDate = new Date(criteria.dateTo);
                toDate.setHours(23, 59, 59, 999); // 包含整天
                if (recordDate > toDate) return false;
            }
            
            return true;
        });
    }

    /**
     * 根据ID获取特定订单
     * @param {string} historyId - 历史记录ID
     * @returns {Object|null} 订单记录
     */
    getOrderById(historyId) {
        const history = this.getHistory();
        return history.find(record => record.id === historyId) || null;
    }

    /**
     * 删除历史订单
     * @param {string} historyId - 历史记录ID
     * @returns {boolean} 是否删除成功
     */
    deleteOrder(historyId) {
        try {
            const currentUser = this.getCurrentUser();
            const allHistory = this.getAllUsersHistory();
            const userHistory = allHistory[currentUser] || [];
            
            const index = userHistory.findIndex(record => record.id === historyId);
            
            if (index === -1) {
                return false;
            }
            
            userHistory.splice(index, 1);
            allHistory[currentUser] = userHistory;
            localStorage.setItem(this.storageKey, JSON.stringify(allHistory));
            
            getLogger().log(`历史订单已删除: ${historyId}`, 'info');
            return true;
            
        } catch (error) {
            getLogger().log(`删除历史订单失败: ${error.message}`, 'error');
            return false;
        }
    }

    /**
     * 清空所有历史记录
     * @returns {boolean} 是否清空成功
     */
    clearHistory() {
        try {
            // 获取当前用户
            const currentUser = this.getCurrentUser();
            const allHistory = this.getAllUsersHistory();
            
            // 只清空当前用户的历史记录
            allHistory[currentUser] = [];
            
            // 保存更新后的数据
            localStorage.setItem(this.storageKey, JSON.stringify(allHistory));
            getLogger().log(`用户 ${currentUser} 的历史订单已清空`, 'info');
            return true;
        } catch (error) {
            getLogger().log(`清空历史订单失败: ${error.message}`, 'error');
            return false;
        }
    }

    /**
     * 清空所有用户的历史记录（管理员功能）
     * @returns {boolean} 是否清空成功
     */
    clearAllHistory() {
        try {
            localStorage.setItem(this.storageKey, JSON.stringify({}));
            getLogger().log('所有用户的历史订单已清空', 'info');
            return true;
        } catch (error) {
            getLogger().log(`清空所有历史订单失败: ${error.message}`, 'error');
            return false;
        }
    }

    /**
     * 导出历史订单数据
     * @param {Array} orders - 要导出的订单数组（可选，默认导出所有）
     * @param {string} format - 导出格式 ('json' | 'csv')
     * @returns {string} 导出的数据字符串
     */
    exportOrders(orders = null, format = 'json') {
        const ordersToExport = orders || this.getHistory();
        
        if (format === 'csv') {
            return this.exportToCSV(ordersToExport);
        } else {
            return JSON.stringify(ordersToExport, null, 2);
        }
    }

    /**
     * 导出为CSV格式
     * @param {Array} orders - 订单数组
     * @returns {string} CSV字符串
     */
    exportToCSV(orders) {
        if (orders.length === 0) return '';
        
        // CSV头部
        const headers = [
            '订单ID', 'OTA参考号', '创建时间', '客户姓名', '客户邮箱', '客户电话',
            '上车地点', '目的地', '日期', '时间', '乘客人数', '行李数量',
            '航班信息', 'OTA渠道', '特殊要求', '创建者'
        ];
        
        // CSV数据行
        const rows = orders.map(record => [
            record.orderId,
            record.orderData.otaReferenceNumber,
            new Date(record.timestamp).toLocaleString('zh-CN'),
            record.orderData.customerName,
            record.orderData.customerEmail,
            record.orderData.customerContact,
            record.orderData.pickup,
            record.orderData.destination,
            record.orderData.date,
            record.orderData.time,
            record.orderData.passengerNumber,
            record.orderData.luggageNumber,
            record.orderData.flightInfo,
            record.orderData.otaChannel,
            record.orderData.specialRequests,
            record.metadata.createdBy
        ]);
        
        // 组合CSV
        const csvContent = [headers, ...rows]
            .map(row => row.map(field => `"${(field || '').toString().replace(/"/g, '""')}"`).join(','))
            .join('\n');
            
        return csvContent;
    }

    /**
     * 生成唯一的历史记录ID
     * @returns {string} 唯一ID
     */
    generateHistoryId() {
        return 'hist_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    /**
     * 复制订单数据到剪贴板（排除价格信息）
     * @param {string} historyId - 历史记录ID
     * @returns {Promise<boolean>} 是否复制成功
     */
    async copyOrderData(historyId) {
        try {
            const order = this.getOrderById(historyId);
            if (!order) {
                getLogger().log(`未找到订单: ${historyId}`, 'error');
                return false;
            }

            // 获取国际化管理器
            const i18nManager = window.getI18nManager ? window.getI18nManager() : null;
            const t = (key, params = {}) => {
                if (i18nManager && i18nManager.t) {
                    return i18nManager.t(key, params);
                }
                // fallback 中文文本
                const fallbacks = {
                    'form.orderId': '订单ID',
                    'form.otaReferenceNumber': 'OTA参考号',
                    'form.flightInfo': '航班信息',
                    'form.subCategory': '服务类型',
                    'form.customerName': '客户姓名',
                    'form.customerContact': '联系电话', 
                    'form.pickupDate': '接送日期',
                    'form.pickupTime': '接送时间',
                    'form.drivingRegion': '行驶区域',
                    'form.pickup': '上车地点',
                    'form.destination': '目的地',
                    'form.carType': '车型',
                    'form.passengerCount': '乘客人数',
                    'form.languages': '需求语言',
                    'form.customerEmail': '客户邮箱',
                    'form.luggageCount': '行李件数',
                    'form.otaChannel': 'OTA渠道',
                    'form.specialRequests': '特殊要求'
                };
                return fallbacks[key] || key;
            };

            // 获取行驶区域名称
            const getDrivingRegionName = (regionId) => {
                const regions = { 1: '吉隆坡', 2: '槟城', 3: '新山', 4: '马六甲', 5: '其他' };
                const regionsEn = { 1: 'Kuala Lumpur', 2: 'Penang', 3: 'Johor Bahru', 4: 'Malacca', 5: 'Others' };
                const isEnglish = i18nManager?.getCurrentLanguage?.() === 'en';
                return isEnglish ? (regionsEn[regionId] || `Region ${regionId}`) : (regions[regionId] || `区域 ${regionId}`);
            };

            // 获取车型名称 - 与API服务中的车型数据保持同步
            const getCarTypeName = (carTypeId) => {
                // 与 api-service.js 中的 carTypes 数据保持同步
                const carTypes = {
                    38: '4座掀背车',
                    5: '5座车',
                    33: '高级5座车(奔驰/宝马)',
                    37: '加长5座车',
                    35: '7座SUV',
                    15: '7座MPV',
                    16: '标准MPV',
                    31: '豪华MPV(Serena)',
                    32: 'Velfire/Alphard',
                    36: 'Alphard',
                    20: '10座MPV/面包车',
                    30: '12座Starex',
                    23: '14座面包车',
                    24: '18座面包车',
                    25: '30座小巴',
                    26: '44座大巴',
                    34: '票券',
                    39: '票券(非马来西亚)'
                };
                const carTypesEn = {
                    38: '4 Seater Hatchback',
                    5: '5 Seater',
                    33: 'Premium 5 Seater (Mercedes/BMW)',
                    37: 'Extended 5',
                    35: '7 Seater SUV',
                    15: '7 Seater MPV',
                    16: 'Standard Size MPV',
                    31: 'Luxury MPV (Serena)',
                    32: 'Velfire/Alphard',
                    36: 'Alphard',
                    20: '10 Seater MPV/Van',
                    30: '12 seat Starex',
                    23: '14 Seater Van',
                    24: '18 Seater Van',
                    25: '30 Seat Mini Bus',
                    26: '44 Seater Bus',
                    34: 'Ticket',
                    39: 'Ticket (Non-Malaysian)'
                };
                const isEnglish = i18nManager?.getCurrentLanguage?.() === 'en';
                return isEnglish ? (carTypesEn[carTypeId] || `Car Type ${carTypeId}`) : (carTypes[carTypeId] || `车型 ${carTypeId}`);
            };

            // 获取服务类型名称
            const getSubCategoryName = (subCategoryId) => {
                // 服务类型映射 - 与API服务中的 subCategories 数据保持同步
                const subCategories = { 2: '接机', 3: '送机', 4: '包车' };
                const subCategoriesEn = { 2: 'Pickup', 3: 'Dropoff', 4: 'Charter' };
                const isEnglish = i18nManager?.getCurrentLanguage?.() === 'en';
                return isEnglish ? (subCategoriesEn[subCategoryId] || `Service ${subCategoryId}`) : (subCategories[subCategoryId] || `服务类型 ${subCategoryId}`);
            };

            // 获取语言名称
            const getLanguageNames = (languagesIdArray) => {
                // 与HTML表单中的语言复选框保持一致
                const languages = { 2: '英文', 4: '中文', 5: 'Paging', 6: 'Charter' };
                const languagesEn = { 2: 'English', 4: 'Chinese', 5: 'Paging', 6: 'Charter' };
                const isEnglish = i18nManager?.getCurrentLanguage?.() === 'en';
                
                if (!languagesIdArray) return null;
                
                let languageIds = [];
                if (Array.isArray(languagesIdArray)) {
                    languageIds = languagesIdArray;
                } else if (typeof languagesIdArray === 'object') {
                    languageIds = Object.values(languagesIdArray).map(id => parseInt(id));
                }
                
                if (languageIds.length === 0) return null;
                
                const names = languageIds.map(id => {
                    return isEnglish ? (languagesEn[id] || `Lang${id}`) : (languages[id] || `语言${id}`);
                });
                return names.join(', ');
            };

            // 构建列表格式的文本 - 按新的顺序排列，与表单字段同步
            const data = order.orderData;
            const copyText = [
                // 1. 订单ID (Order ID) - 使用 order.orderId
                `${t('form.orderId')}: ${order.orderId || 'N/A'}`,
                // 2. OTA参考号 (OTA Reference Number) - 对应 otaReferenceNumber 字段
                `${t('form.otaReferenceNumber')}: ${data.otaReferenceNumber || 'N/A'}`,
                // 3. 航班信息 (Flight Info) - 对应 flightInfo 字段
                `${t('form.flightInfo')}: ${data.flightInfo || 'N/A'}`,
                // 4. 订单类型 (Service Type) - 对应 subCategoryId 字段，显示映射后的名称
                ...(data.subCategoryId ? [`${t('form.subCategory')}: ${getSubCategoryName(data.subCategoryId)}`] : []),
                // 5. 客户姓名 (Customer Name) - 对应 customerName 字段
                `${t('form.customerName')}: ${data.customerName || 'N/A'}`,
                // 6. 客户联系电话 (Contact Phone) - 对应 customerContact 字段
                `${t('form.customerContact')}: ${data.customerContact || 'N/A'}`,
                // 7. 接送日期 (Pickup Date) - 对应 date 字段
                `${t('form.pickupDate')}: ${data.date || 'N/A'}`,
                // 8. 接送时间 (Pickup Time) - 对应 time 字段
                `${t('form.pickupTime')}: ${data.time || 'N/A'}`,
                // 9. 行驶区域 (Driving Region) - 对应 drivingRegionId 字段 (如果有)
                ...(data.drivingRegionId ? [`${t('form.drivingRegion')}: ${getDrivingRegionName(data.drivingRegionId)}`] : []),
                // 10. 上车地点 (Pickup Location) - 对应 pickup 字段
                `${t('form.pickup')}: ${data.pickup || 'N/A'}`,
                // 11. 目的地 (Destination) - 对应 destination 字段
                `${t('form.destination')}: ${data.destination || 'N/A'}`,
                // 12. 车型 (Car Type) - 对应 carTypeId 字段 (如果有)
                ...(data.carTypeId ? [`${t('form.carType')}: ${getCarTypeName(data.carTypeId)}`] : []),
                // 13. 乘客人数 (Passenger Count) - 对应 passengerNumber 字段
                `${t('form.passengerCount')}: ${data.passengerNumber || 'N/A'}`,
                // 14. 语言要求 (Required Languages) - 对应 languagesIdArray 字段 (如果有)
                ...(getLanguageNames(data.languagesIdArray) ? [`${t('form.languages')}: ${getLanguageNames(data.languagesIdArray)}`] : [])
            ].filter(line => !line.endsWith('N/A')); // 过滤掉N/A的行

            const finalText = copyText.join('\n');

            // 复制到剪贴板
            if (navigator.clipboard && navigator.clipboard.writeText) {
                await navigator.clipboard.writeText(finalText);
                getLogger().log('订单信息已复制到剪贴板', 'success');
                return true;
            } else {
                // Fallback for older browsers
                const textArea = document.createElement('textarea');
                textArea.value = finalText;
                textArea.style.position = 'fixed';
                textArea.style.left = '-999999px';
                textArea.style.top = '-999999px';
                document.body.appendChild(textArea);
                textArea.focus();
                textArea.select();
                const result = document.execCommand('copy');
                document.body.removeChild(textArea);
                
                if (result) {
                    getLogger().log('订单信息已复制到剪贴板（fallback方式）', 'success');
                    return true;
                }
            }
            
            return false;
            
        } catch (error) {
            getLogger().log(`复制订单失败: ${error.message}`, 'error');
            return false;
        }
    }

    /**
     * 获取统计信息
     * @returns {Object} 统计数据
     */
    getStatistics() {
        const history = this.getHistory();
        const now = new Date();
        const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        const startOfWeek = new Date(today);
        startOfWeek.setDate(today.getDate() - today.getDay());
        const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);

        return {
            total: history.length,
            today: history.filter(o => new Date(o.timestamp) >= today).length,
            week: history.filter(o => new Date(o.timestamp) >= startOfWeek).length,
            month: history.filter(o => new Date(o.timestamp) >= startOfMonth).length
        };
    }

    /**
     * 显示历史订单面板
     * @description 获取并渲染历史订单数据，然后显示面板。
     */
    showHistoryPanel() {
        const logger = getLogger(); // 获取 logger 实例
        logger.log('显示历史订单面板', 'info'); // 记录日志
        const historyPanel = document.getElementById('historyPanel'); // 获取历史面板元素
        if (historyPanel) {
            try {
                const orders = this.getHistory(); // 获取所有历史订单
                logger.log(`获取到 ${orders.length} 条历史订单`, 'info');
                
                this.renderHistory(orders); // 渲染订单列表
                this.updateStatistics(orders); // 更新统计信息
                this.updateRecordCount(orders.length); // 更新记录数量
                
                historyPanel.classList.remove('hidden'); // 移除 hidden 类以显示面板
                historyPanel.style.display = 'block'; // 确保显示样式
                // 不禁用body滚动，让历史面板内部可滚动
                // document.body.style.overflow = 'hidden'; // 注释掉这行，避免滚动问题
                
                // 确保面板在最前面
                historyPanel.style.zIndex = '9999';
                
                logger.log('历史订单面板已显示', 'success');
            } catch (error) {
                logger.log(`显示历史订单面板失败: ${error.message}`, 'error');
                // 显示错误状态
                this.renderErrorState(error.message);
                historyPanel.classList.remove('hidden');
                historyPanel.style.display = 'block';
            }
        } else {
            logger.log('历史订单面板元素未找到', 'error');
        }
    }

    /**
     * 隐藏历史订单面板
     * @description 关闭并隐藏历史订单面板。
     */
    hideHistoryPanel() {
        const logger = getLogger(); // 获取 logger 实例
        logger.log('隐藏历史订单面板', 'info'); // 记录日志
        const historyPanel = document.getElementById('historyPanel'); // 获取历史面板元素
        if (historyPanel) {
            historyPanel.classList.add('hidden'); // 添加 hidden 类以隐藏面板
            historyPanel.style.display = 'none'; // 确保隐藏样式
            document.body.style.overflow = ''; // 恢复背景滚动
            
            logger.log('历史订单面板已隐藏', 'success');
        }
    }

    /**
     * 渲染历史订单列表
     * @param {Array<object>} orders - 要渲染的订单数组。
     */
    renderHistory(orders) {
        const container = document.getElementById('historyListContainer'); // 获取列表容器
        const logger = getLogger(); // 获取 logger 实例

        if (!container) {
            logger.log('历史订单列表容器未找到', 'error'); // 如果容器不存在，记录错误
            return;
        }

        logger.log(`开始渲染 ${orders.length} 条历史订单`, 'info');

        if (orders.length === 0) {
            // 如果没有订单，显示空状态
            container.innerHTML = `
                <div class="empty-state">
                    <div class="empty-icon">📝</div>
                    <div class="empty-text">暂无历史订单</div>
                </div>
            `;
            logger.log('显示空状态', 'info');
        } else {
            // 如果有订单，则生成并插入订单项HTML
            try {
                const ordersHtml = orders.map((order, index) => {
                    // 判断订单状态 - 安全检查orderId类型
                    const orderIdStr = String(order.orderId || '');
                    const isSuccess = order.metadata?.apiResponse?.success !== false && 
                                    !orderIdStr.startsWith('FAILED_') && 
                                    !orderIdStr.startsWith('EXCEPTION_');
                    const statusClass = isSuccess ? 'success' : 'failed';
                    const statusText = isSuccess ? '✅ 成功' : '❌ 失败';
                    const statusColor = isSuccess ? '#28a745' : '#dc3545';
                    
                    // 格式化时间
                    let timeStr = 'N/A';
                    try {
                        timeStr = new Date(order.timestamp).toLocaleString();
                    } catch (timeError) {
                        logger.log(`订单 ${index} 时间格式化失败: ${timeError.message}`, 'warning');
                    }
                    
                    return `
                    <div class="history-item ${statusClass}" data-id="${order.id || 'unknown'}">
                        <div class="history-item-header">
                            <span class="history-item-id">订单ID: ${order.orderId || 'N/A'}</span>
                            <span class="history-item-status" style="color: ${statusColor}; font-weight: bold;">${statusText}</span>
                            <span class="history-item-time">${timeStr}</span>
                            <button class="copy-order-btn" data-order-id="${order.id}" title="复制订单信息" style="
                                background: #28a745;
                                color: white;
                                border: none;
                                border-radius: 4px;
                                padding: 4px 8px;
                                font-size: 12px;
                                cursor: pointer;
                                margin-left: 10px;
                                transition: background-color 0.2s;
                            " onmouseover="this.style.backgroundColor='#218838'" onmouseout="this.style.backgroundColor='#28a745'">📋 复制</button>
                        </div>
                        <div class="history-item-content">
                            <p><strong>客户:</strong> ${order.orderData?.customerName || 'N/A'}</p>
                            <p><strong>服务:</strong> ${order.orderData?.subCategoryId || 'N/A'}</p>
                            <p><strong>OTA参考号:</strong> ${order.orderData?.otaReferenceNumber || 'N/A'}</p>
                            <p><strong>上车地点:</strong> ${order.orderData?.pickup || 'N/A'}</p>
                            <p><strong>目的地:</strong> ${order.orderData?.destination || 'N/A'}</p>
                            ${!isSuccess ? `<p><strong>错误信息:</strong> <span style="color: #dc3545;">${order.metadata?.apiResponse?.message || '未知错误'}</span></p>` : ''}
                        </div>
                    </div>
                    `;
                }).join('');
                
                container.innerHTML = ordersHtml;
                
                // 绑定复制按钮事件
                this.bindCopyButtonEvents();
                
                logger.log('历史订单列表渲染完成', 'success');
            } catch (renderError) {
                logger.log(`渲染历史订单失败: ${renderError.message}`, 'error');
                this.renderErrorState('渲染订单列表时发生错误');
            }
        }
    }

    /**
     * 渲染错误状态
     * @param {string} errorMessage - 错误信息
     */
    renderErrorState(errorMessage) {
        const container = document.getElementById('historyListContainer');
        if (container) {
            container.innerHTML = `
                <div class="empty-state">
                    <div class="empty-icon">⚠️</div>
                    <div class="empty-text">加载失败: ${errorMessage}</div>
                    <button onclick="location.reload()" style="margin-top: 10px; padding: 5px 10px;">重新加载</button>
                </div>
            `;
        }
    }

    /**
     * 更新统计信息
     * @param {Array} orders - 订单数组
     */
    updateStatistics(orders) {
        try {
            const stats = this.getStatistics();
            
            // 更新统计显示
            const statElements = {
                statTotal: document.getElementById('statTotal'),
                statToday: document.getElementById('statToday'),
                statWeek: document.getElementById('statWeek'),
                statMonth: document.getElementById('statMonth')
            };

            if (statElements.statTotal) statElements.statTotal.textContent = stats.total;
            if (statElements.statToday) statElements.statToday.textContent = stats.today;
            if (statElements.statWeek) statElements.statWeek.textContent = stats.week;
            if (statElements.statMonth) statElements.statMonth.textContent = stats.month;

            const logger = getLogger();
            logger?.log('统计信息已更新', 'info', stats);
        } catch (error) {
            const logger = getLogger();
            logger?.log(`更新统计信息失败: ${error.message}`, 'error');
        }
    }

    /**
     * 更新记录数量显示
     * @param {number} count - 记录数量
     */
    updateRecordCount(count) {
        try {
            const listCountElement = document.getElementById('listCount');
            if (listCountElement) {
                listCountElement.textContent = count;
            }
            
            // 如果有国际化管理器，更新翻译
            const i18nManager = window.getI18nManager ? window.getI18nManager() : null;
            if (i18nManager) {
                const recordCountElement = document.querySelector('[data-i18n="history.recordCount"]');
                if (recordCountElement) {
                    const translatedText = i18nManager.t('history.recordCount', { count });
                    if (translatedText) {
                        recordCountElement.innerHTML = translatedText.replace('{count}', `<span id="listCount">${count}</span>`);
                    }
                }
            }
        } catch (error) {
            const logger = getLogger();
            logger?.log(`更新记录数量失败: ${error.message}`, 'error');
        }
    }

    /**
     * 绑定复制按钮事件
     */
    bindCopyButtonEvents() {
        const copyButtons = document.querySelectorAll('.copy-order-btn');
        copyButtons.forEach(button => {
            button.addEventListener('click', async (e) => {
                e.stopPropagation();
                const orderId = button.getAttribute('data-order-id');
                const originalText = button.textContent;
                
                try {
                    button.disabled = true;
                    button.textContent = '复制中...';
                    
                    const success = await this.copyOrderData(orderId);
                    
                    if (success) {
                        button.textContent = '✅ 已复制';
                        button.style.backgroundColor = '#28a745';
                        
                        // 显示成功提示
                        const i18nManager = window.getI18nManager ? window.getI18nManager() : null;
                        const message = i18nManager?.t('messages.copied') || '已复制';
                        if (window.getUIManager && window.getUIManager().showQuickToast) {
                            window.getUIManager().showQuickToast(message, 'success');
                        }
                        
                        // 2秒后恢复原状
                        setTimeout(() => {
                            button.textContent = originalText;
                            button.style.backgroundColor = '#28a745';
                            button.disabled = false;
                        }, 2000);
                    } else {
                        button.textContent = '❌ 失败';
                        button.style.backgroundColor = '#dc3545';
                        
                        // 显示失败提示
                        const i18nManager = window.getI18nManager ? window.getI18nManager() : null;
                        const message = i18nManager?.t('messages.copyFailed') || '复制失败';
                        if (window.getUIManager && window.getUIManager().showQuickToast) {
                            window.getUIManager().showQuickToast(message, 'error');
                        }
                        
                        // 2秒后恢复原状
                        setTimeout(() => {
                            button.textContent = originalText;
                            button.style.backgroundColor = '#28a745';
                            button.disabled = false;
                        }, 2000);
                    }
                } catch (error) {
                    getLogger().log(`复制按钮处理失败: ${error.message}`, 'error');
                    button.textContent = '❌ 错误';
                    button.style.backgroundColor = '#dc3545';
                    
                    setTimeout(() => {
                        button.textContent = originalText;
                        button.style.backgroundColor = '#28a745';
                        button.disabled = false;
                    }, 2000);
                }
            });
        });
    }

    /**
     * 初始化历史订单面板的事件监听器
     * @description 为关闭、清空、导出和搜索按钮绑定事件。
     */
    initEventListeners() {
        const logger = getLogger();
        
        // 关闭按钮
        const closeBtn = document.getElementById('closeHistoryBtn');
        if (closeBtn) {
            closeBtn.addEventListener('click', () => this.hideHistoryPanel());
            logger?.log('历史订单关闭按钮事件已绑定', 'info');
        } else {
            logger?.log('历史订单关闭按钮未找到', 'warning');
        }
        
        // 清空历史按钮
        const clearBtn = document.getElementById('clearHistoryBtn');
        if (clearBtn) {
            clearBtn.addEventListener('click', () => {
                if (confirm('确定要清空所有历史订单吗？此操作不可恢复。')) {
                    this.clearHistory();
                    this.renderHistory([]);
                    this.updateStatistics([]);
                    this.updateRecordCount(0);
                    logger?.log('历史订单已清空', 'success');
                }
            });
            logger?.log('历史订单清空按钮事件已绑定', 'info');
        }
        
        // 导出按钮
        const exportBtn = document.getElementById('exportHistoryBtn');
        if (exportBtn) {
            exportBtn.addEventListener('click', () => this.exportOrders());
            logger?.log('历史订单导出按钮事件已绑定', 'info');
        }
        
        // 搜索按钮
        const searchBtn = document.getElementById('searchHistoryBtn');
        if (searchBtn) {
            searchBtn.addEventListener('click', () => {
                const criteria = {
                    orderId: document.getElementById('searchOrderId')?.value || '',
                    customer: document.getElementById('searchCustomer')?.value || '',
                    dateFrom: document.getElementById('searchDateFrom')?.value || '',
                    dateTo: document.getElementById('searchDateTo')?.value || '',
                };
                const results = this.searchOrders(criteria);
                this.renderHistory(results);
                this.updateRecordCount(results.length);
                logger?.log(`搜索完成，找到 ${results.length} 条记录`, 'info');
            });
            logger?.log('历史订单搜索按钮事件已绑定', 'info');
        }
        
        // 重置搜索按钮
        const resetBtn = document.getElementById('resetSearchBtn');
        if (resetBtn) {
            resetBtn.addEventListener('click', () => {
                ['searchOrderId', 'searchCustomer', 'searchDateFrom', 'searchDateTo'].forEach(id => {
                    const element = document.getElementById(id);
                    if (element) element.value = '';
                });
                
                const allOrders = this.getHistory();
                this.renderHistory(allOrders);
                this.updateRecordCount(allOrders.length);
                logger?.log('搜索已重置', 'info');
            });
            logger?.log('历史订单重置按钮事件已绑定', 'info');
        }
    }
}

// 创建全局实例 - 延迟创建以避免依赖问题
let orderHistoryManagerInstance = null;

// 安全创建实例
try {
    orderHistoryManagerInstance = new OrderHistoryManager();
} catch (error) {
    console.error('❌ 历史订单管理器初始化失败:', error);
    // 实例将在 getOrderHistoryManager() 中重新创建
}

/**
 * 获取历史订单管理器实例
 * @returns {OrderHistoryManager} 管理器实例
 */
function getOrderHistoryManager() {
    if (!orderHistoryManagerInstance) {
        console.log('🔧 创建历史订单管理器实例...');
        orderHistoryManagerInstance = new OrderHistoryManager();
    }
    return orderHistoryManagerInstance;
}

// 导出到全局作用域
window.OrderHistoryManager = OrderHistoryManager;
window.getOrderHistoryManager = getOrderHistoryManager;

// 确保OTA命名空间存在并暴露管理器
window.OTA = window.OTA || {};
window.OTA.OrderHistoryManager = OrderHistoryManager;
window.OTA.getOrderHistoryManager = getOrderHistoryManager;
window.OTA.orderHistoryManager = orderHistoryManagerInstance; // 直接使用已创建的实例

// 向后兼容
window.orderHistoryManager = orderHistoryManagerInstance; // 直接使用已创建的实例
