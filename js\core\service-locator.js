/**
 * 依赖标签（Dependency Tags）
 * 文件: js/core/service-locator.js
 * 角色: 服务定位器（快捷 getter：getAppState/getLogger/getApiService/getUIManager 等）
 * 上游依赖(直接使用): DependencyContainer
 * 下游被依赖(常见调用方): 大多数业务模块（建议优先使用 getService('name')）
 * 事件: 无；仅封装访问
 * 更新时间: 2025-08-09
 */
/**
 * 服务定位器 - 统一服务获取接口
 * 解决当前系统中 window.OTA.xxx || window.xxx 的双重模式问题
 * 
 * 使用方式:
 * - 替换所有 getAppState() 为 getService('appState')
 * - 替换所有 getLogger() 为 getService('logger')
 * - 统一所有依赖获取方式
 */

// 确保OTA命名空间和依赖容器存在
window.OTA = window.OTA || {};

(function() {
    'use strict';

    /**
     * 服务定位器类
     * 提供统一的服务获取接口，兼容旧的获取方式
     */
    class ServiceLocator {
        constructor() {
            this.container = null;
            this.fallbackMap = new Map();
            this.migrationWarnings = new Set();
        }

        /**
         * 初始化服务定位器
         * @param {DependencyContainer} container - 依赖容器实例
         */
        init(container) {
            this.container = container;
            this.setupFallbackMap();
            // 减法开发：不再在定位器中注册核心服务，统一由 ApplicationBootstrap 负责注册
            console.log('✅ 服务定位器已初始化');
        }

        /**
         * 设置降级映射
         * 用于兼容旧的获取方式
         */
        setupFallbackMap() {
            // 核心服务
            this.fallbackMap.set('appState', () => window.OTA.appState || window.appState);
            // 日志器提供安全降级到 console，避免早期初始化阶段报错
            this.fallbackMap.set('logger', () => window.OTA.logger || window.logger || console);
            this.fallbackMap.set('apiService', () => window.OTA.apiService || window.apiService);
            this.fallbackMap.set('geminiService', () => window.OTA.geminiService || window.geminiService);
            this.fallbackMap.set('uiManager', () => window.OTA.uiManager || window.uiManager);
            this.fallbackMap.set('utils', () => window.OTA.utils || window.utils);
            this.fallbackMap.set('i18nManager', () => window.OTA.i18nManager || window.i18nManager);
            
            // 功能管理器
            this.fallbackMap.set('formManager', () => window.OTA.formManager || window.formManager);
            this.fallbackMap.set('imageUploadManager', () => window.OTA.imageUploadManager || window.imageUploadManager);
            this.fallbackMap.set('currencyConverter', () => window.OTA.currencyConverter || window.currencyConverter);
            this.fallbackMap.set('multiOrderManager', () => window.OTA.multiOrderManager || window.multiOrderManager);
            this.fallbackMap.set('orderHistoryManager', () => window.OTA.orderHistoryManager || window.orderHistoryManager);
            this.fallbackMap.set('pagingServiceManager', () => window.OTA.pagingServiceManager || window.pagingServiceManager);
            
            // 多订单系统服务
            this.fallbackMap.set('multiOrderDetector', () => window.OTA.multiOrderDetector || this.createMultiOrderService('MultiOrderDetector'));
            this.fallbackMap.set('multiOrderProcessor', () => window.OTA.multiOrderProcessor || this.createMultiOrderService('MultiOrderProcessor'));
            this.fallbackMap.set('multiOrderRenderer', () => window.OTA.multiOrderRenderer || this.createMultiOrderService('MultiOrderRenderer'));
            this.fallbackMap.set('batchProcessor', () => window.OTA.batchProcessor || this.createMultiOrderService('BatchProcessor'));
            this.fallbackMap.set('multiOrderStateManager', () => window.OTA.multiOrderStateManager || this.createMultiOrderService('MultiOrderStateManager'));
            this.fallbackMap.set('multiOrderCoordinator', () => window.OTA.multiOrderCoordinator || this.createMultiOrderService('MultiOrderCoordinator'));
            this.fallbackMap.set('multiOrderManagerV2', () => window.OTA.multiOrderManagerV2 || this.createMultiOrderService('MultiOrderManagerV2'));
        }

        /**
         * 注册核心服务到依赖容器
         */
    registerCoreServices() { /* 已移除：由 ApplicationBootstrap 统一注册 */ }

        /**
         * 获取服务实例
         * @param {string} serviceName - 服务名称
         * @returns {any} 服务实例
         */
        getService(serviceName) {
            // 优先从依赖容器获取
            if (this.container && this.container.has(serviceName)) {
                try {
                    return this.container.get(serviceName);
                } catch (error) {
                    console.warn(`从依赖容器获取 ${serviceName} 失败，尝试降级方案:`, error.message);
                }
            }

            // 降级到旧的获取方式
            if (this.fallbackMap.has(serviceName)) {
                const service = this.fallbackMap.get(serviceName)();
                
                // 发出迁移警告（每个服务只警告一次）
                if (!this.migrationWarnings.has(serviceName)) {
                    console.warn(`⚠️ 服务 ${serviceName} 使用了降级获取方式，建议迁移到依赖容器`);
                    this.migrationWarnings.add(serviceName);
                }
                
                return service;
            }

            // 最后尝试直接从全局获取
            const globalService = window.OTA[serviceName] || window[serviceName];
            if (globalService) {
                console.warn(`⚠️ 服务 ${serviceName} 从全局获取，建议注册到依赖容器`);
                return globalService;
            }

            throw new Error(`服务 ${serviceName} 未找到`);
        }

        /**
         * 检查服务是否可用
         * @param {string} serviceName - 服务名称
         * @returns {boolean}
         */
        hasService(serviceName) {
            try {
                const service = this.getService(serviceName);
                return !!service;
            } catch {
                return false;
            }
        }

        /**
         * 获取所有可用服务
         * @returns {string[]}
         */
        getAvailableServices() {
            const services = new Set();
            
            // 从依赖容器获取
            if (this.container) {
                this.container.getRegisteredServices().forEach(name => services.add(name));
            }
            
            // 从降级映射获取
            this.fallbackMap.forEach((_, name) => services.add(name));
            
            // 从全局获取
            Object.keys(window.OTA || {}).forEach(name => services.add(name));
            
            return Array.from(services);
        }

        /**
         * 创建默认的AppState实例
         */
    createAppState() { /* 减法开发：不再创建默认实例，改由上游显式提供 */ }

        /**
         * 获取Logger实例 - 简化版本
         * 减法开发：删除冗余创建逻辑，直接返回已存在的实例
         */
    createLogger() { return console; }

        /**
         * 创建默认的ApiService实例
         */
    createApiService() { /* 已移除默认实现 */ }

        /**
         * 创建默认的GeminiService实例
         */
    createGeminiService() { /* 已移除默认实现 */ }

        /**
         * 创建默认的UIManager实例
         */
    createUIManager() { /* 已移除默认实现 */ }

        /**
         * 创建默认的Utils实例
         */
    createUtils() { /* 已移除默认实现 */ }

        /**
         * 创建默认的FormManager实例
         */
    createFormManager() { /* 已移除默认实现 */ }

        /**
         * 获取多订单服务实例 - 简化版本
         * 减法开发：删除复杂创建逻辑，直接返回已存在的实例
         * @param {string} serviceName - 服务类名
         * @returns {Object|null} 服务实例或null
         */
        createMultiOrderService(serviceName) {
            // 尝试从OTA命名空间获取已存在的实例
            if (window.OTA) {
                // 优先查找完整类名
                let instance = window.OTA[serviceName];
                if (instance) return instance;
                
                // 查找小写变体 (例如 MultiOrderDetector -> multiOrderDetector)
                const lowerCaseName = serviceName.charAt(0).toLowerCase() + serviceName.slice(1);
                instance = window.OTA[lowerCaseName];
                if (instance) return instance;
            }
            
            // 减法开发：不创建降级实例，直接返回null
            // 让调用方处理null情况，而不是创建假实例
            return null;
        }


        /**
         * 获取Logger实例
         * @returns {Object} Logger实例
         */
        getLogger() {
            // 尝试从已注册的服务获取
            if (this.container && this.container.has('logger')) {
                try {
                    return this.container.get('logger');
                } catch (error) {
                    // 降级到基础console logger
                }
            }
            
            // 降级方案
            return {
                // 兼容多签名：
                // 1) log(message)
                // 2) log(message, level)
                // 3) log(message, data)
                // 4) log(message, level, data)
                log: (...args) => {
                    const message = args[0];
                    let level = 'info';
                    let data;

                    if (typeof args[1] === 'string') {
                        level = args[1];
                        data = args[2];
                    } else if (typeof args[1] !== 'undefined') {
                        // 第二个参数是数据对象
                        data = args[1];
                    }

                    const levelStr = typeof level === 'string' ? level : String(level || 'info');
                    const prefix = `[${levelStr.toUpperCase()}]`;

                    // 根据级别选择合适的控制台方法
                    const method = (lvl => {
                        switch ((lvl || '').toLowerCase()) {
                            case 'error': return 'error';
                            case 'warn':
                            case 'warning': return 'warn';
                            case 'info':
                            case 'success': return 'info';
                            default: return 'log';
                        }
                    })(levelStr);

                    if (typeof data !== 'undefined') {
                        console[method](`${prefix} ${String(message)}`, data);
                    } else {
                        console[method](`${prefix} ${String(message)}`);
                    }
                },
                logError: (message, error) => {
                    // 兼容 logError(error) 与 logError(message, error)
                    if (message instanceof Error && typeof error === 'undefined') {
                        console.error(`[ERROR] ${message.message}`, message);
                    } else {
                        console.error(`[ERROR] ${String(message)}`, error);
                    }
                }
            };
        }

        /**
         * 获取迁移状态报告
         * @returns {Object}
         */
        getMigrationReport() {
            return {
                totalServices: this.getAvailableServices().length,
                containerServices: this.container ? this.container.getRegisteredServices().length : 0,
                fallbackUsed: this.migrationWarnings.size,
                warnings: Array.from(this.migrationWarnings),
                recommendations: [
                    '将所有服务注册到依赖容器',
                    '替换直接的全局访问为 getService() 调用',
                    '移除双重获取模式 (window.OTA.xxx || window.xxx)'
                ]
            };
        }
    }

    // 创建全局服务定位器实例
    const serviceLocator = new ServiceLocator();

    // 等待依赖容器准备就绪后初始化
    if (window.OTA.container) {
        serviceLocator.init(window.OTA.container);
    } else {
        // 延迟初始化
        setTimeout(() => {
            if (window.OTA.container) {
                serviceLocator.init(window.OTA.container);
            }
        }, 100);
    }

    // 暴露到OTA命名空间
    window.OTA.serviceLocator = serviceLocator;

    // 提供统一的服务获取函数
    window.OTA.getService = function(serviceName) {
        return serviceLocator.getService(serviceName);
    };

    // 向后兼容的全局函数
    window.getService = window.OTA.getService;

    // 提供便捷的服务获取函数（逐步替换旧的获取方式）
    window.getAppState = () => serviceLocator.getService('appState');
    // 始终提供可靠的Logger（必要时降级到console）
    window.getLogger = () => serviceLocator.getLogger();
    window.getAPIService = () => serviceLocator.getService('apiService');
    window.getApiService = () => serviceLocator.getService('apiService'); // 兼容大小写变体
    window.getGeminiService = () => serviceLocator.getService('geminiService');
    window.getUIManager = () => serviceLocator.getService('uiManager');
    window.getUtils = () => serviceLocator.getService('utils');

    // 多订单系统服务获取函数
    window.getMultiOrderDetector = () => serviceLocator.getService('multiOrderDetector');
    window.getMultiOrderProcessor = () => serviceLocator.getService('multiOrderProcessor');
    window.getMultiOrderRenderer = () => serviceLocator.getService('multiOrderRenderer');
    window.getBatchProcessor = () => serviceLocator.getService('batchProcessor');
    window.getMultiOrderStateManager = () => serviceLocator.getService('multiOrderStateManager');
    window.getMultiOrderCoordinator = () => serviceLocator.getService('multiOrderCoordinator');
    window.getMultiOrderManagerV2 = () => serviceLocator.getService('multiOrderManagerV2');

    console.log('✅ 服务定位器已加载');

})();
