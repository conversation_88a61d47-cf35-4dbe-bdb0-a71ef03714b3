<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>渠道检测修复验证</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 3px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>渠道检测修复验证测试</h1>
    <div id="results"></div>

    <!-- 加载必要的脚本 -->
    <script src="js/base-utils/logger.js"></script>
    <script src="js/strategies/fliggy-ota-strategy.js"></script>
    <script src="js/strategies/jingge-ota-strategy.js"></script>
    <script src="js/ota-system/config/channel-patterns.json"></script>
    <script src="js/ota-system/ota-channel-detector.js"></script>

    <script>
        const testOrderData = `订单编号：2870626119501574181买家：芦荟的斑点支付时间：2025-08-09 19:26:46
查看详情

豪华7座

【接机】

新加坡-新加坡

[出发]樟宜机场T4

[抵达]新加坡滨海宾乐雅酒店

约22.9公里

HO1601

[预计抵达]

2025-08-10 14:45:00

王湖皎

真实号：15216832557

---
4成人1儿童

司机姓名：kk`;

        function runTests() {
            const resultsDiv = document.getElementById('results');
            let html = '';

            // 测试1: 验证FliggyOTAStrategy.detectFromContent方法
            html += '<div class="test-section">';
            html += '<h2>测试1: FliggyOTAStrategy.detectFromContent 方法</h2>';
            
            if (typeof FliggyOTAStrategy !== 'undefined' && typeof FliggyOTAStrategy.detectFromContent === 'function') {
                try {
                    const fliggyResult = FliggyOTAStrategy.detectFromContent(testOrderData);
                    html += '<p class="success">✅ FliggyOTAStrategy.detectFromContent 方法存在</p>';
                    html += '<pre>' + JSON.stringify(fliggyResult, null, 2) + '</pre>';
                    
                    if (fliggyResult.detectedChannel === 'fliggy' && fliggyResult.confidence > 0.9) {
                        html += '<p class="success">✅ Fliggy渠道检测成功</p>';
                    } else {
                        html += '<p class="error">❌ Fliggy渠道检测失败</p>';
                    }
                } catch (error) {
                    html += '<p class="error">❌ 方法调用失败: ' + error.message + '</p>';
                }
            } else {
                html += '<p class="error">❌ FliggyOTAStrategy.detectFromContent 方法不存在</p>';
            }
            html += '</div>';

            // 测试2: 验证JingGeOTAStrategy.detectFromContent方法
            html += '<div class="test-section">';
            html += '<h2>测试2: JingGeOTAStrategy.detectFromContent 方法</h2>';
            
            if (typeof JingGeOTAStrategy !== 'undefined' && typeof JingGeOTAStrategy.detectFromContent === 'function') {
                try {
                    const jinggeResult = JingGeOTAStrategy.detectFromContent(testOrderData);
                    html += '<p class="success">✅ JingGeOTAStrategy.detectFromContent 方法存在</p>';
                    html += '<pre>' + JSON.stringify(jinggeResult, null, 2) + '</pre>';
                } catch (error) {
                    html += '<p class="error">❌ 方法调用失败: ' + error.message + '</p>';
                }
            } else {
                html += '<p class="error">❌ JingGeOTAStrategy.detectFromContent 方法不存在</p>';
            }
            html += '</div>';

            // 测试3: 验证提示词片段
            html += '<div class="test-section">';
            html += '<h2>测试3: 提示词片段验证</h2>';
            
            if (typeof FliggyOTAStrategy !== 'undefined') {
                try {
                    const snippets = FliggyOTAStrategy.getFieldPromptSnippets();
                    html += '<p class="success">✅ Fliggy提示词片段获取成功</p>';
                    html += '<pre>' + JSON.stringify(snippets, null, 2) + '</pre>';
                    
                    const requiredFields = ['ota', 'ota_price', 'car_type_id'];
                    const missingFields = requiredFields.filter(field => !snippets[field]);
                    
                    if (missingFields.length === 0) {
                        html += '<p class="success">✅ 所有必需字段都存在</p>';
                    } else {
                        html += '<p class="error">❌ 缺少字段: ' + missingFields.join(', ') + '</p>';
                    }
                } catch (error) {
                    html += '<p class="error">❌ 提示词片段获取失败: ' + error.message + '</p>';
                }
            }
            html += '</div>';

            // 测试4: 正则表达式验证
            html += '<div class="test-section">';
            html += '<h2>测试4: 正则表达式验证</h2>';
            
            const fliggyPattern = /订单编号[：:\s]*\d{19}/g;
            const matches = testOrderData.match(fliggyPattern);
            
            html += '<p class="info">正则表达式: ' + fliggyPattern.toString() + '</p>';
            html += '<p class="info">匹配结果: ' + JSON.stringify(matches) + '</p>';
            
            if (matches && matches.length > 0) {
                html += '<p class="success">✅ 正则表达式匹配成功</p>';
            } else {
                html += '<p class="error">❌ 正则表达式匹配失败</p>';
            }
            html += '</div>';

            resultsDiv.innerHTML = html;
        }

        // 页面加载完成后运行测试
        window.addEventListener('load', function() {
            setTimeout(runTests, 1000); // 延迟1秒确保所有脚本加载完成
        });
    </script>
</body>
</html>
