/**
 * 影子部署管理器 - 功能连续性保障
 * 
 * 设计目标：
 * - 支持新旧系统并行运行
 * - 提供实时对比验证
 * - 确保零中断切换
 * - 支持即时回滚
 * 
 * <AUTHOR>
 * @version 1.0.0
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};
window.OTA.core = window.OTA.core || {};

(function() {
    'use strict';

    /**
     * 影子部署管理器
     * 管理新旧系统的并行运行和切换
     */
    class ShadowDeployment {
        constructor() {
            this.shadowSystems = new Map();
            this.productionSystems = new Map();
            this.comparisonResults = [];
            this.trafficSplitRatio = 0; // 0-100，表示发送到影子系统的流量百分比
            this.logger = this.getLogger();
            this.featureToggle = window.OTA.featureToggle;
            
            this.logger.log('影子部署管理器已初始化');
        }

        /**
         * 注册影子系统
         * @param {string} systemName - 系统名称
         * @param {Object} shadowSystem - 影子系统实例
         * @param {Object} productionSystem - 生产系统实例
         */
        registerShadowSystem(systemName, shadowSystem, productionSystem) {
            this.shadowSystems.set(systemName, shadowSystem);
            this.productionSystems.set(systemName, productionSystem);
            
            this.logger.log(`影子系统已注册: ${systemName}`);
        }

        /**
         * 处理请求（影子部署模式）
         * @param {string} systemName - 系统名称
         * @param {string} method - 方法名称
         * @param {Array} args - 参数
         * @returns {Promise<any>} 处理结果
         */
        async processRequest(systemName, method, ...args) {
            const productionSystem = this.productionSystems.get(systemName);
            const shadowSystem = this.shadowSystems.get(systemName);
            
            if (!productionSystem) {
                throw new Error(`Production system not found: ${systemName}`);
            }

            // 始终执行生产系统
            const productionResult = await this.executeWithErrorHandling(
                productionSystem, method, args, 'production'
            );

            // 根据特性开关和流量分割决定是否执行影子系统
            if (this.shouldExecuteShadow(systemName)) {
                // 异步执行影子系统，不阻塞生产流程
                this.executeShadowAsync(systemName, shadowSystem, method, args, productionResult);
            }

            return productionResult;
        }

        /**
         * 判断是否应该执行影子系统
         * @param {string} systemName - 系统名称
         * @returns {boolean} 是否执行影子系统
         */
        shouldExecuteShadow(systemName) {
            // 检查特性开关
            const featureKey = `enableShadow${systemName}`;
            if (!this.featureToggle?.isEnabled(featureKey)) {
                return false;
            }

            // 检查流量分割
            if (this.trafficSplitRatio <= 0) {
                return false;
            }

            // 随机决定是否执行（基于流量分割比例）
            return Math.random() * 100 < this.trafficSplitRatio;
        }

        /**
         * 异步执行影子系统
         * @param {string} systemName - 系统名称
         * @param {Object} shadowSystem - 影子系统
         * @param {string} method - 方法名称
         * @param {Array} args - 参数
         * @param {any} productionResult - 生产系统结果
         */
        async executeShadowAsync(systemName, shadowSystem, method, args, productionResult) {
            try {
                const shadowResult = await this.executeWithErrorHandling(
                    shadowSystem, method, args, 'shadow'
                );

                // 对比结果
                this.compareResults(systemName, method, args, productionResult, shadowResult);
                
            } catch (error) {
                this.logger.log(`影子系统执行失败: ${systemName}.${method}`, 'error', {
                    error: error.message,
                    args: args
                });
            }
        }

        /**
         * 安全执行方法
         * @param {Object} system - 系统实例
         * @param {string} method - 方法名称
         * @param {Array} args - 参数
         * @param {string} type - 系统类型
         * @returns {Promise<any>} 执行结果
         */
        async executeWithErrorHandling(system, method, args, type) {
            const startTime = Date.now();
            
            try {
                if (!system || typeof system[method] !== 'function') {
                    throw new Error(`Method ${method} not found in ${type} system`);
                }

                const result = await system[method](...args);
                const duration = Date.now() - startTime;
                
                this.logger.log(`${type}系统执行成功: ${method} (${duration}ms)`, 'debug');
                
                return result;
                
            } catch (error) {
                const duration = Date.now() - startTime;
                
                this.logger.log(`${type}系统执行失败: ${method} (${duration}ms)`, 'error', {
                    error: error.message,
                    stack: error.stack
                });
                
                throw error;
            }
        }

        /**
         * 对比生产和影子系统的结果
         * @param {string} systemName - 系统名称
         * @param {string} method - 方法名称
         * @param {Array} args - 参数
         * @param {any} productionResult - 生产结果
         * @param {any} shadowResult - 影子结果
         */
        compareResults(systemName, method, args, productionResult, shadowResult) {
            const comparison = {
                timestamp: Date.now(),
                systemName,
                method,
                args: this.sanitizeArgs(args),
                productionResult: this.sanitizeResult(productionResult),
                shadowResult: this.sanitizeResult(shadowResult),
                isMatch: this.deepEqual(productionResult, shadowResult),
                differences: this.findDifferences(productionResult, shadowResult)
            };

            this.comparisonResults.push(comparison);

            // 内存管理：参数化结果上限与修剪阈值（可回退）
            const maxResults = (window.OTA?.config?.shadow?.maxResults) ?? 1000;
            const trimTo = (window.OTA?.config?.shadow?.trimTo) ?? 500;

            if (this.comparisonResults.length > maxResults) {
                const keep = Math.max(0, Math.min(trimTo, maxResults));
                this.comparisonResults = this.comparisonResults.slice(-keep);
            }

            // 记录不匹配的结果
            if (!comparison.isMatch) {
                this.logger.log(`影子系统结果不匹配: ${systemName}.${method}`, 'warn', {
                    differences: comparison.differences
                });
            }

            // 触发对比事件
            if (window.OTA.eventCoordinator) {
                window.OTA.eventCoordinator.emit('shadow-comparison', comparison);
            }
        }

        /**
         * 深度比较两个对象
         * @param {any} obj1 - 对象1
         * @param {any} obj2 - 对象2
         * @returns {boolean} 是否相等
         */
        deepEqual(obj1, obj2) {
            if (obj1 === obj2) return true;
            
            if (obj1 == null || obj2 == null) return obj1 === obj2;
            
            if (typeof obj1 !== typeof obj2) return false;
            
            if (typeof obj1 !== 'object') return obj1 === obj2;
            
            const keys1 = Object.keys(obj1);
            const keys2 = Object.keys(obj2);
            
            if (keys1.length !== keys2.length) return false;
            
            for (let key of keys1) {
                if (!keys2.includes(key)) return false;
                if (!this.deepEqual(obj1[key], obj2[key])) return false;
            }
            
            return true;
        }

        /**
         * 查找差异
         * @param {any} obj1 - 对象1
         * @param {any} obj2 - 对象2
         * @returns {Array} 差异列表
         */
        findDifferences(obj1, obj2) {
            const differences = [];
            
            // 简化的差异检测
            if (typeof obj1 !== typeof obj2) {
                differences.push({
                    type: 'type_mismatch',
                    production: typeof obj1,
                    shadow: typeof obj2
                });
            }
            
            return differences;
        }

        /**
         * 清理参数（移除敏感信息）
         * @param {Array} args - 参数数组
         * @returns {Array} 清理后的参数
         */
        sanitizeArgs(args) {
            return args.map(arg => {
                if (typeof arg === 'object' && arg !== null) {
                    return { ...arg, password: '[REDACTED]', token: '[REDACTED]' };
                }
                return arg;
            });
        }

        /**
         * 清理结果（移除敏感信息）
         * @param {any} result - 结果
         * @returns {any} 清理后的结果
         */
        sanitizeResult(result) {
            if (typeof result === 'object' && result !== null) {
                return { ...result, password: '[REDACTED]', token: '[REDACTED]' };
            }
            return result;
        }

        /**
         * 设置流量分割比例
         * @param {number} ratio - 比例（0-100）
         */
        setTrafficSplitRatio(ratio) {
            this.trafficSplitRatio = Math.max(0, Math.min(100, ratio));
            this.logger.log(`流量分割比例已设置: ${this.trafficSplitRatio}%`);
        }

        /**
         * 获取对比结果统计
         * @returns {Object} 统计信息
         */
        getComparisonStats() {
            const total = this.comparisonResults.length;
            const matches = this.comparisonResults.filter(r => r.isMatch).length;
            const mismatches = total - matches;
            
            return {
                total,
                matches,
                mismatches,
                matchRate: total > 0 ? (matches / total * 100).toFixed(2) : 0
            };
        }

        /**
         * 清理对比结果
         */
        clearComparisonResults() {
            this.comparisonResults = [];
            this.logger.log('对比结果已清理');
        }

        /**
         * 获取Logger实例
         * @returns {Object} Logger实例
         */
        getLogger() {
            try {
                if (typeof getLogger === 'function') {
                    return getLogger();
                }
                return {
                    log: (message, level = 'info', data = null) => {
                        console.log(`[ShadowDeployment][${level.toUpperCase()}] ${message}`, data || '');
                    }
                };
            } catch (error) {
                return {
                    log: (message, level = 'info', data = null) => {
                        console.log(`[ShadowDeployment][${level.toUpperCase()}] ${message}`, data || '');
                    }
                };
            }
        }
    }

    // 创建全局实例（受特性开关影响的惰性使用）
    const shadowDeployment = new ShadowDeployment();

    // 暴露到OTA命名空间
    window.OTA.core.ShadowDeployment = ShadowDeployment;
    window.OTA.shadowDeployment = shadowDeployment;

    console.log('✅ 影子部署管理器已加载');
    try {
        const enabled = window.OTA?.featureToggle?.isEnabled('enableShadowDeployment');
        if (!enabled) {
            console.log('🛰️ 影子部署处于关闭状态（受特性开关控制）');
        }
    } catch (_) { /* 忽略 */ }

})();
